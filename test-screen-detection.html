<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>录屏检测测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .video-container {
            position: relative;
            margin: 20px 0;
            background: #000;
            border-radius: 8px;
            overflow: hidden;
        }
        
        video {
            width: 100%;
            height: 400px;
            object-fit: cover;
        }
        
        .log-container {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        
        button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            margin: 0 10px;
            cursor: pointer;
            font-size: 16px;
        }
        
        button:hover {
            background: #0056CC;
        }
        
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
        }
        
        .status.safe {
            background: #d4edda;
            color: #155724;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .status.danger {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 录屏检测测试工具</h1>
        
        <div class="status safe" id="status">
            ✅ 状态正常 - 未检测到录屏行为
        </div>
        
        <div class="video-container">
            <video id="testVideo" controls>
                <source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4">
                您的浏览器不支持视频播放
            </video>
        </div>
        
        <div class="controls">
            <button onclick="startDetection()">🔍 开始检测</button>
            <button onclick="stopDetection()">⏹️ 停止检测</button>
            <button onclick="clearLogs()">🗑️ 清空日志</button>
            <button onclick="testScreenCapture()">📹 测试屏幕捕获</button>
        </div>
        
        <div class="log-container" id="logContainer">
            <div>等待开始检测...</div>
        </div>
    </div>

    <script src="src/utils/screenRecordingDetector.js"></script>
    <script>
        let detector = null;
        let logContainer = document.getElementById('logContainer');
        let statusElement = document.getElementById('status');
        let video = document.getElementById('testVideo');

        // 添加日志
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            
            if (type === 'warning') {
                logEntry.style.color = '#ffaa00';
            } else if (type === 'error') {
                logEntry.style.color = '#ff4444';
            } else if (type === 'success') {
                logEntry.style.color = '#44ff44';
            }
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 更新状态显示
        function updateStatus(message, type = 'safe') {
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        // 开始检测
        function startDetection() {
            if (detector) {
                addLog('检测已在运行中', 'warning');
                return;
            }
            
            detector = new ScreenRecordingDetector();
            detector.startDetection();
            
            addLog('✅ 录屏检测已启动', 'success');
            updateStatus('🔍 正在监控录屏行为...', 'warning');
            
            // 监听检测事件
            window.addEventListener('screenRecordingDetected', (event) => {
                addLog(`🚨 检测到录屏! 方法: ${event.detail.method}`, 'error');
                updateStatus('🚨 检测到录屏行为!', 'danger');
                
                // 简单的视频保护演示
                video.style.filter = 'brightness(0)';
            });
            
            window.addEventListener('screenRecordingRestored', (event) => {
                addLog(`✅ 录屏停止! 方法: ${event.detail.method}`, 'success');
                updateStatus('✅ 录屏行为已停止', 'safe');
                
                // 恢复视频显示
                video.style.filter = '';
            });
        }

        // 停止检测
        function stopDetection() {
            if (detector) {
                detector.stopDetection();
                detector = null;
                addLog('⏹️ 录屏检测已停止', 'info');
                updateStatus('⏹️ 检测已停止', 'safe');
                
                // 恢复视频
                video.style.filter = '';
            }
        }

        // 清空日志
        function clearLogs() {
            logContainer.innerHTML = '<div>日志已清空...</div>';
        }

        // 测试屏幕捕获（会触发检测）
        async function testScreenCapture() {
            try {
                addLog('🧪 测试屏幕捕获API...', 'info');
                const stream = await navigator.mediaDevices.getDisplayMedia({
                    video: true,
                    audio: false
                });
                
                addLog('📹 屏幕捕获成功，应该会触发检测', 'warning');
                
                // 3秒后停止捕获
                setTimeout(() => {
                    stream.getTracks().forEach(track => track.stop());
                    addLog('⏹️ 屏幕捕获已停止', 'info');
                }, 3000);
                
            } catch (error) {
                addLog(`❌ 屏幕捕获失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后的提示
        window.addEventListener('load', () => {
            addLog('📄 页面加载完成，点击"开始检测"按钮开始测试', 'info');
            addLog('💡 测试方法：', 'info');
            addLog('  1. 点击"开始检测"', 'info');
            addLog('  2. 打开任意录屏软件（OBS、QuickTime等）', 'info');
            addLog('  3. 观察日志输出和视频变化', 'info');
            addLog('  4. 关闭录屏软件，观察恢复情况', 'info');
        });
    </script>
</body>
</html>
