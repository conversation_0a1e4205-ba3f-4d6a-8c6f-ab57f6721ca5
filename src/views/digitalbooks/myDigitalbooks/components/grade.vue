<template>
  <div class="grade-wrap">
    <div class="header mb-10">
      <img src="@/assets/images/skyclass/arrow-back.png" alt="返回按钮" @click="back" />
      <span class="back" @click="back">返回</span>
    </div>
    <div class="grade-title">
      <template v-if="school">
        <div>{{ school.name }}</div>
        <div class="btn-active" @click="bindSchoolShow = true">更换学校</div>
      </template>
      <template v-else>
        <div>暂未绑定学校</div>
        <div class="btn-active" @click="bindSchoolShow = true">绑定学校</div>
      </template>
    </div>
    <div class="table-box">
      <div class="table-title">
        <div></div>
        <div>
          <div class="btn-active" @click="enterInterestClassShow = true">
            创建班级/组群
          </div>
        </div>
      </div>
      <el-table
        :data="tableData"
        :header-row-style="{ background: '#F8FAFF' }"
        :header-cell-style="{
          background: '#F8FAFF',
          color: '#3479FF',
          border: 'none',
          'font-weight': '400',
        }"
        :header-row-class-name="tableHeader"
        :row-class-name="rowClass"
        :cell-class-name="cellClass"
        :height="'calc(100% - 70px)'"
        style="width: 100%"
      >
        <el-table-column prop="name" align="left" label="班级名称">
          <template slot-scope="scope">
            <div class="flex items-center">
              <div>{{ scope.row.name }}</div>
              <!-- <div>
                <img style="vertical-align: middle;" src="../../../../assets/parent/assistant-code.svg" />
              </div> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column align="left" label="学校名称">
          <template slot-scope="scope">
            <div class="pointer">
              <span v-if="scope.row.school">{{ scope.row.school.name }}</span>
              <span v-else class="item-handle" @click="openJionSchool(scope.row)">加入学校</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="left" label="学生名单">
          <template slot-scope="scope">
            <div class="pointer" @click="showStuLists(scope.row.userExtra)">{{ scope.row.userExtra | stuList }}</div>
            <!-- <el-button class="item-handle" type="text" @click="inputStu(scope.row)">导入学生</el-button> -->
            <el-button class="item-handle" type="text" @click="handleStuShare(scope.row)">邀请学生</el-button>
          </template>
        </el-table-column>

        <el-table-column align="left" label="在学教材">
          <template slot-scope="scope">
            <el-popover placement="bottom-start">
              <div class="pop-box">
                <div
                  v-for="item in getDigitalBooks(scope.row.courseList)"
                  :key="item.id"
                  class="item-scope"
                >
                  {{ (item.digitalBook && item.digitalBook.title) }}
                </div>
              </div>
              <div slot="reference">
                <template v-if="getDigitalBooks(scope.row.courseList).length">
                  <div
                    v-for="(book, index) in getDigitalBooks(scope.row.courseList).slice(0, 2)"
                    :key="index"
                    class="item-scope"
                  >
                    {{ (book.digitalBook && book.digitalBook.title) }}
                  </div>
                  <div
                    v-if="getDigitalBooks(scope.row.courseList).length > 2"
                    class="tl"
                  >
                    ...
                  </div>
                </template>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="" width="300" align="left" label="操作">
          <template slot-scope="scope">
            <span class="item-handle" @click="gotoDetail(scope.row)">详情</span>
            <span class="item-handle" @click="handleAddBook(scope.row)">添加教材</span>
            <span class="item-handle-del" @click="handleRemove(scope.row)">删除班级</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <ComponentDialog
      :width="'30vw'"
      :title="'删除'"
      :dialog-visible="removeVisible"
      :is-center="true"
      @closeDialog="
        removeVisible = false
      "
    >
      <div class="flex flex-col w items-center">
        <div class="f14" style="margin-bottom: 30px; line-height: 40px">
          删除班级后，该班级相关信息将全部清除不可找回， 确认删除吗？
        </div>
        <div class="flex justify-around w">
          <div
            class="edu-btn-opacity f14"
            @click="
              removeVisible = false
            "
          >
            放弃
          </div>
          <div class="edu-btn f14" @click="remove">确定删除</div>
        </div>
      </div>
    </ComponentDialog>
    <ComponentDialog
      :width="'30vw'"
      :title="'删除'"
      :dialog-visible="jionSchoolShow"
      :is-center="true"
      @closeDialog="
        jionSchoolShow = false
      "
    >
      <div class="flex flex-col w items-center">
        <div class="f14" style="margin-bottom: 30px; line-height: 40px">
          将当前班级加入到绑定的学校，此操作将无法撤销，确认加入吗？
        </div>
        <div class="flex justify-around w">
          <div
            class="edu-btn-opacity f14"
            @click="
              jionSchoolShow = false
            "
          >
            放弃
          </div>
          <div class="edu-btn f14" @click="jionSchool">确定加入</div>
        </div>
      </div>
    </ComponentDialog>
    <!-- 创建班级 -->
    <jionClass
      v-if="addClassShow"
      :show="addClassShow"
      :append-to-body="true"
      :need-show-code="false"
      @close="addClassShow = false; _getUserClassList()"
    />

    <!-- 绑定学校 -->
    <bindSchool v-if="bindSchoolShow" :show="bindSchoolShow" :append-to-body="true" @close="bindSchoolShow = false" />

    <!-- 添加教材 -->
    <addBook
      v-if="addBookShow"
      :row="nowEditRow"
      :user-id="id"
      :show="addBookShow"
      :append-to-body="true"
      @close="addBookShow = false"
      @added="addBookShow = false; _getUserClassList()"
    />

    <NormalDialog
      v-if="inputStuListShow"
      width="30vw"
      height="25vw"
      :title="'导入学生名单'"
      :dialog-visible="inputStuListShow"
      :is-center="true"
      :has-footer="false"
      @closeDialog="closeInputStuList"
    >
      <div class="w">
        <div class="input-excel">
          <div class="input-box w">
            {{ fileName }}
          </div>
          <div>
            <el-upload
              ref="upload"
              :action="uploadUrl"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
              :on-change="handleChange"
              :auto-upload="false"
              :file-list="fileList"
              :data="{ user_id: inputInfo.userId }"
            >
              <el-button class="item-handle" type="text">选择文件</el-button>
            </el-upload>
          </div>
        </div>
        <div class="w flex" style="align-items: center">
          <el-button class="item-handle" type="text" @click="donwloadExcel">下载模版Excel</el-button>
          <div class="item-tip">（为确保准确识别，请下载此模版用于名单录入）</div>
        </div>
        <div class="w flex justify-center mb20">
          <div :class="fileList.length > 0 ? 'btn-active' : 'btn'" @click="inputSubmit">{{ isUploading ? '正在上传' : '上传' }}
          </div>
        </div>
      </div>
    </NormalDialog>

    <el-drawer
      title=""
      :visible.sync="drawer"
      :direction="'btt'"
      :show-close="true"
      :with-header="false"
      :size="'100%'"
      :custom-class="'grade-drawer'"
      :close-on-press-escape="false"
    >

      <div class="top">
        <svg-icon icon-class="close" class-name="close-svg" @click="drawer = false" />
      </div>
      <div class="bottom">
        <div class="drawer-title">
          <div class="line"></div>
          <div>学生名单</div>
          <div class="stu-len">
            共{{ stuLists.length }}人
          </div>
          <!-- <div class="btn-active" @click="drawer = false; inputStuListShow = true;">导入名单</div> -->
        </div>
        <div class="drawer-detail">
          <div v-for="(item, index) in stuLists" :key="index" class="stu-item">
            {{ item }}
          </div>
        </div>
      </div>
    </el-drawer>

    <NormalDialog
      v-if="codeShow"
      width="35vw"
      :title="codeInfo && codeInfo.name"
      :dialog-visible="codeShow"
      :is-center="true"
      @closeDialog="codeShow = false"
    >
      <div class="w flex flex-col items-center">
        <div ref="bill" class="w flex flex-col items-center">
          <div class="code-share-title flex items-center">{{ codeInfo && codeInfo.name }}</div>
          <div ref="qrCodeUrl" class="qrcode mb-10"></div>
          <div class="code-share flex items-center">
            班级二维码，保存或截图分享
          </div>
        </div>
        <div class="mt20 flex justify-center">
          <el-button type="text" class="f16" @click="saveImg">保存</el-button>
        </div>
      </div>
    </NormalDialog>
    <NormalDialog
      v-if="enterInterestClassShow"
      width="30vw"
      :title="'添加班级/组群'"
      :dialog-visible="enterInterestClassShow"
      :append-to-body="true"
      :is-center="true"
      @closeDialog="
        enterInterestClassShow = false;nameValue=''
      "
    >
      <div class="w flex-col">
        <div class="flex items-center mb10">
          <div>班级名称：</div>
          <el-input v-model="nameValue" style="flex: 1" class="w" placeholder="请输入班级名称" />
        </div>
      </div>
      <template #footer>
        <div class="edu-btn" @click="addClass('INTEREST')">确定</div>
      </template>
    </NormalDialog>
    <NormalDialog
      v-if="stuCodeShow"
      width="30vw"
      :title="'邀请'"
      :dialog-visible="stuCodeShow"
      :is-center="true"
      @closeDialog="stuCodeShow = false"
    >
      <div class="w flex justify-center items-center">
        <div>
          <!-- <div ref="qrCodeUrl1" class="qrcode mb-10" style="margin-left: 20%;"></div> -->
          <div class="w flex justify-center items-center">
            <p>邀请号：{{ urlH5 }}</p>
            <!-- <div v-clipboard:copy="urlH5" v-clipboard:success="onCopy" v-clipboard:error="onError" class="copy">复制</div> -->
          </div>
        </div>
      </div>
    </NormalDialog>
  </div>
</template>

<script>
import QRCode from 'qrcodejs2'
import html2canvas from 'html2canvas'
import moment from 'moment'
import NormalDialog from '@/components/classPro/NormalDialog/index.vue'
import { mapGetters } from 'vuex'
import { getUserClassList, assistantDeleteClass, userClassBindSchool, assistantAddClass } from '@/api/educational-api.js'
import { getClassInfo } from '@/api/digital-api.js'
import bindSchool from '@/components/classPro/ClassBind/bindSchool.vue'
import jionClass from '@/components/classPro/ClassBind/jionClass.vue'
import addBook from '@/components/classPro/ClassBind/addBook.vue'
import { debounce } from '@/utils/index'
import ComponentDialog from '@/components/classPro/ComponentDialog'
export default {
  components: {
    NormalDialog,
    bindSchool,
    jionClass,
    addBook,
    ComponentDialog
  },
  filters: {
    formTime (val) {
      return moment(val).format('YYYY/MM/DD HH:mm')
    },
    classType (val) {
      const obj = {
        'ORGANIZATION': '行政班',
        'INTEREST': '兴趣班'
      }
      return obj[val]
    },
    techName (val) {
      const arr = []
      for (let i = 0; i < val.length; i++) {
        arr.push(val[i].displayName)
      }
      return arr.join('、')
    },
    stuList (val) {
      if (val) {
        if (val.studentList) {
          const arr = val.studentList.split(',')
          return arr.length + '人>'
        }
      }
      return ''
    }
  },
  props: {
    fromType: {
      type: String,
      default: '' // classSetting: 班级管理
    }
  },
  data () {
    return {
      urlH5: '',
      stuCodeShow: false,
      code: null,
      codeInfo: null,
      codeShow: false,
      addBookShow: false,
      addClassShow: false,
      bindSchoolShow: false,
      removeVisible: false,
      jionSchoolShow: false,
      enterInterestClassShow: false,
      tableData: [],
      tableDataMap: null,
      stuLists: [],
      drawer: false,
      inputInfo: [],
      inputStuListShow: false,
      nowEditRow: null,
      fileName: '',
      nameValue: '',
      fileList: [],
      isUploading: false,
      uploadUrl: process.env.VUE_APP_ADMIN_API + '/home/<USER>/rosterImport'
    }
  },
  computed: {
    ...mapGetters([
      'school',
      'id'
    ])
  },
  mounted () {
    this._getUserClassList()
  },
  methods: {
    getDigitalBooks(courseList) {
      return courseList ? courseList.filter(item => { return item.courseType === 'DIGITAL_BOOK' }) : []
    },
    addClass: debounce(async function (type) {
      const obj = {
        type: type
      }
      if (type === 'INTEREST') {
        if (!this.nameValue) {
          this.$message.error('请填写内容')
          return
        }
        obj.name = this.nameValue
      }
      await assistantAddClass(obj)
      this.enterInterestClassShow = false
      this.nameValue = ''
      this._getUserClassList()
    }, 3000, true),
    openJionSchool (row) {
      if (!this.school) {
        this.$message.error('请先绑定学校')
        return
      }
      this.nowEditRow = row
      this.jionSchoolShow = true
    },
    async jionSchool () {
      try {
        await this.$confirm('确认将该班级加入学校吗，此操作会将班级的学生同步加入到学校?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await userClassBindSchool({ classId: this.nowEditRow.userId })
        this.jionSchoolShow = false
        this._getUserClassList()
      } catch (error) {
        if (error !== 'cancel') {
          console.log(error)
        }
      }
    },
    handleRemove (row) {
      this.nowEditRow = row
      this.removeVisible = true
    },
    remove: debounce(async function () {
      try {
        await assistantDeleteClass({ childUserId: this.nowEditRow.userId })
        this.removeVisible = false
        this._getUserClassList()
      } catch (error) {
        this.removeVisible = false
      }
    }, 3000, true),
    back() {
      this.$emit('back')
    },
    tableHeader (row, rowIndex) {
      return 'table-header'
    },
    rowClass (row, rowIndex) {
      return 'row-class'
    },
    cellClass (row, rowIndex) {
      return 'cell-class'
    },
    async _getUserClassList () {
      const { data } = await getUserClassList({
        scene: 'DIGITAL_CLASS'
      })
      this.tableData = data
      const map = new Map()
      for (let i = 0; i < data.length; i++) {
        map.set(data[i].userId, data[i])
      }
      this.tableDataMap = map
    },
    handleAddBook (row) {
      this.nowEditRow = row
      this.addBookShow = true
    },
    gotoDetail (row) {
      this.$router.push({ path: '/educational/diginfo?t=' + row.token })
    },
    showStuLists (row) {
      this.drawer = true
      this.stuLists = []
      this.inputInfo = row
      if (row) {
        if (row.studentList) {
          const arr = row.studentList.split(',')
          this.stuLists = arr
        }
      }
    },
    inputStu (row) {
      this.fileName = ''
      this.fileList = []
      this.inputInfo = row
      this.inputStuListShow = true
    },
    inputSubmit () {
      if (this.isUploading || this.fileList.length === 0) {
        return
      }
      this.isUploading = true
      if (this.fileList.length > 0) {
        this.$refs.upload.submit()
      } else {
        return
      }
    },
    handleAvatarSuccess (res, file) {
      if (res.status + '' === '1') {
        this.$message.success('导入成功')
      } else {
        this.$message.error(res.content)
      }
      this._getUserClassList()
      this.fileList = []
      this.fileName = ''
      this.inputStuListShow = false
      this.isUploading = false
    },
    handleChange (file, fileList) {
      const index = file.name.lastIndexOf('.')
      // 获取后缀
      const ext = file.name.substr(index + 1)
      const isXls = ['xls', 'xlsx'].indexOf(ext.toLowerCase()) !== -1
      if (!isXls) {
        this.$message.error('请上传Excel文件')
        this.fileList = []
        this.fileName = ''
      } else {
        this.fileList = [fileList[fileList.length - 1]]
        this.fileName = file.name
      }
    },
    closeInputStuList () {
      this.inputStuListShow = false
      this.fileList = []
      this.fileName = ''
    },
    beforeAvatarUpload (file) {
      const index = file.name.lastIndexOf('.')
      // 获取后缀
      const ext = file.name.substr(index + 1)
      const isXls = ['xls'].indexOf(ext.toLowerCase()) !== -1
      if (!isXls) {
        this.$message.error('请上传Excel文件')
        return Promise.reject()
      }
      return isXls
    },
    donwloadExcel () {
      const blobUrl = process.env.VUE_APP_ADMIN_API + '/Public/download/班级学生名单导入模板.xls'
      const downloadElement = document.createElement('a')
      downloadElement.href = blobUrl
      // 下载后文件名
      downloadElement.download = '班级学生名单导入模板.xls'
      document.body.appendChild(downloadElement)
      // 点击下载
      downloadElement.click()
      // 下载完成移除元素
      document.body.removeChild(downloadElement)
      if (window.URL.revokeObjectURL == null || window.URL.revokeObjectURL) {
        // 释放掉blob对象
        window.URL.revokeObjectURL(blobUrl)
      }
    },
    creatQrCode (row) {
      const origin = window.location.origin
      const url = `${origin}/#/parent/hassClassInfo?isScan=1&classId=${row.userId}&className=${row.name}&schoolId=${row.school && row.school.id}&schoolName=${row.school && row.school.name}`
      this.codeShow = true
      this.codeInfo = row
      this.$nextTick(
        () => {
          new QRCode(this.$refs.qrCodeUrl, {
            width: 200, // 二维码宽度
            height: 200, // 二维码高度
            text: url, // 浏览器地址url
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.H
          })
          this.$refs.qrCodeUrl.removeAttribute('title')
        }
      )
    },
    dataURLToBlob (dataurl) {
      const arr = dataurl.split(',')
      const mime = arr[0].match(/:(.*?);/)[1]
      const bstr = atob(arr[1])
      let n = bstr.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new Blob([u8arr], { type: mime })
    },
    saveImage () {
      const canvasID = this.$refs.bill
      const that = this
      const a = document.createElement('a')
      html2canvas(canvasID).then((canvas) => {
        const dom = document.body.appendChild(canvas)
        dom.style.display = 'none'
        a.style.display = 'none'
        document.body.removeChild(dom)
        const blob = that.dataURLToBlob(dom.toDataURL('image/png'))
        a.setAttribute('href', URL.createObjectURL(blob))
        // 这块是保存图片操作  可以设置保存的图片的信息
        a.setAttribute('download', that.codeInfo.name + '.png')
        document.body.appendChild(a)
        a.click()
        URL.revokeObjectURL(blob)
        document.body.removeChild(a)
      })
    },
    saveImg () {
      this.saveImage()
    },
    // 邀请学生
    async handleStuShare (row) {
      const { data } = await getClassInfo({
        classUserId: row.userId
      })
      this.urlH5 = data.inviteCode
      this.stuCodeShow = true
      this.$nextTick(
        () => {
          new QRCode(this.$refs.qrCodeUrl1, {
            width: 200, // 二维码宽度
            height: 200, // 二维码高度
            text: this.urlH5, // 浏览器地址url
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.H
          })
          this.$refs.qrCodeUrl.removeAttribute('title')
        }
      )
    },
    onCopy () {
      this.$message.success('内容已复制到剪切板！')
    },
    onError () {
      this.$message.error('抱歉，复制失败！')
    }
  }
}
</script>

<style lang="scss" scoped>
.header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        img {
            width: 14px;
            height: 14px;
            object-fit: contain;
            margin-right: 5px;
            cursor: pointer;
        }

        span {
            font-weight: 400;
            font-size: 14px;
            color: #1C1B1A;
            line-height: 20px;
            cursor: pointer;
        }
}
.mt10 {
  margin-top: 10px;
}

.mb10 {
  margin-bottom: 10px;
}

.w70 {
  width: 70px;
}

.f14 {
  font-size: 14px;
}

.f20 {
  font-size: 20px;
}

.school-disc {
  margin-top: 10px;
}

.school-disc2 {
  width: 5px;
  height: 5px;
  background: #828282;
  border-radius: 50%;
  margin-right: 5px;
}

.grade-wrap {
  width: 100%;
  height: 100%;
  padding: 10px;

  .grade-title {
    height: 60px;
    border: 1px solid #c3d5f9;
    border-radius: 5px;
    padding: 0 20px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-L);
  }

  .table-box {
    height: calc(100% - 90px);

    .table-title {
      height: 50px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 500;
      font-size: var(--font-size-XXL);
      color: #1c1b1a;
      padding-right: 20px;

      .icon-i {
        width: 3px;
        height: 16px;
        background: #3479ff;
        border-radius: 1px;
        margin-right: 5px;
      }
    }
  }

  .btn,
  .btn-active {
    width: 80px;
    height: 28px;
    border-radius: 5px;
    font-size: var(--font-size-L);
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }

  .btn {
    background: #aaaaaa;
  }

  .btn-active {
    background: #3479ff;
  }

  .tr {
    text-align: right;
  }

  .tl {
    text-align: left;
  }

  .item-scope {
    width: 100%;
    font-size: var(--font-size-L);
    @include ellipses(1);
  }

  .item-tip {
    height: 100%;
    display: flex;
    align-items: center;
    font-size: var(--font-size-S);
    color: #595959;
  }

  .item-handle {
    color: #3479ff;
  }

  .item-handle-dis {
    color: #595959;
  }

  .item-handle-del {
    color: #ff3434;
  }

  .item-handle-del,
  .item-handle-dis,
  .item-handle {
    font-size: var(--font-size-L);
    cursor: pointer;
    margin-right: 10px;
    text-decoration: underline;
  }

  .mb10 {
    margin-bottom: 10px;
  }

  .f14 {
    font-size: 14px;
  }
}

.pop-box {
  max-height: 300px;
  overflow-y: auto;

  .item-scope {
    margin-bottom: 10px;
    font-size: var(--font-size-L); // 避免自动适配

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>

<style lang="scss" scoped>
.grade-wrap {
  .table-header {
    font-weight: 500;
    font-size: var(--font-size-L);
    color: #3479ff;

    th {
      background: #f8faff;
    }

    .el-table__cell {
      border-bottom: none;
      padding: 0px;
    }
  }

  .cell-class {
    //font-size: 14px;
    font-size: var(--font-size-L);
  }

  .row-class {
    font-weight: 400;
    font-size: var(--font-size-L);
    color: #0e0e0e;
    letter-spacing: 0.22px;
  }
}

.item-scope {
  @include ellipses(1);
}

.input-excel {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;

  .input-box {
    width: 300px;
    height: 40px;
    background: #FFFFFF;
    border: 1px solid #3479FF;
    border-radius: 3px;
    display: flex;
    align-items: center;
    padding: 0 15px;
  }
}

.mb20 {
  margin-bottom: 20px;
}

.top {
  // height: 400px;
  // display: flex;
  // padding: 338px 30px 0 0;
  position: fixed;
  bottom: 210px;
  right: 10px;

  .close-svg {
    width: 36PX !important;
    height: 36PX !important;
    margin-left: auto;
    cursor: pointer;
  }
}

.bottom {
  position: fixed;
  bottom: 0px;
  // height: calc(100% - 400px);
  height: 200px;
  width: 100%;
  background: #FFFFFF;
  border-radius: 40px 40px 0px 0px;
  padding: 20px 20px 0 20px;

  .drawer-title {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    font-weight: 500;
    font-size: var(--font-size-L);
    color: #1C1B1A;
    box-sizing: border-box;

    .line {
      width: 2px;
      height: 14px;
      background: #3479FF;
      margin-right: 10px;
    }

    .stu-len {
      margin-left: 20px;
      margin-right: 50px;
      font-weight: 500;
      font-size: var(--font-size-L);
      color: #3479FF;
    }
  }

  .drawer-detail {
    margin-top: 20px;
    width: 100%;
    height: calc(100% - 60px);
    overflow-y: auto;

    .stu-item {
      padding: 20px 20px;
      box-sizing: border-box;
      font-size: var(--font-size-L);
      color: #19191A;
      display: inline-block;
    }
  }
}

.code-share {
  font-size: 16px;
  padding: 10px 0;
  color: #000000;
}

.code-share-title {
  font-size: 18px;
  padding: 10px 0;
  color: #000000;
  font-weight: 600;
}

.btn-jihuo {
  width: 140px;
  height: 35px;
  border-radius: 5px;
  font-size: 14px;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  background: #3479ff;
  margin-top: 50px;
}

.jihuo-text {
  color: #000;
  font-size: 14px;
  margin-top: 150px;

  .fb {
    font-weight: 500;
  }
}

.copy {
  width: 65px;
  height: 33px;
  line-height: 33px;
  background: #3479FF;
  border-radius: 5px;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  text-align: center;
  cursor: pointer;
  margin-left: 10px;
}
</style>

<style lang='scss'>
.grade-drawer {
  width: 100%;
  height: 100%;
}
</style>
