<script>
export default {
  name: '<PERSON><PERSON><PERSON>hub',
  data() {
    return {
      showIframe: false,
      jupyterUrl: '/jupyterhub',
      isElectron: false
    }
  },
  mounted() {
    this.isElectron = window.navigator.userAgent.includes('Electron')
  },
  methods: {
    toggleIframe() {
      console.log('=== toggleIframe 调试信息 ===')
      console.log('当前是否在 Electron 环境:', this.isElectron)
      console.log('当前 showIframe 状态:', this.showIframe)
      console.log('用户代理:', navigator.userAgent)

      if (this.showIframe) {
        console.log('准备关闭 iframe，检测 Jupyter dirty 状态...')
        this.checkJupyterDirtyState()
      }

      this.showIframe = !this.showIframe
      console.log('iframe 状态已切换为:', this.showIframe)
      console.log('=== toggleIframe 调试信息结束 ===')
    },

    checkJupyterDirtyState() {
      try {
        const iframe = this.$refs.jupyterIframe
        console.log('iframe 元素:', iframe)

        if (!iframe) {
          console.log('无法获取 iframe 元素')
          return
        }

        console.log('iframe.contentWindow:', iframe.contentWindow)

        if (!iframe.contentWindow) {
          console.log('无法访问 iframe.contentWindow')
          return
        }

        const contentWindow = iframe.contentWindow

        console.log('contentWindow.Jupyter:', contentWindow.Jupyter)
        console.log('contentWindow.IPython:', contentWindow.IPython)

        if (contentWindow.Jupyter && contentWindow.Jupyter.notebook) {
          console.log('找到 Jupyter.notebook')
          console.log('Jupyter.notebook:', contentWindow.Jupyter.notebook)
          console.log('Jupyter.notebook.dirty:', contentWindow.Jupyter.notebook.dirty)
        } else {
          console.log('未找到 Jupyter.notebook')
        }

        if (contentWindow.IPython && contentWindow.IPython.notebook) {
          console.log('找到 IPython.notebook')
          console.log('IPython.notebook:', contentWindow.IPython.notebook)
          console.log('IPython.notebook.dirty:', contentWindow.IPython.notebook.dirty)
        } else {
          console.log('未找到 IPython.notebook')
        }

        console.log('页面标题:', contentWindow.document.title)
        console.log('beforeunload 事件:', contentWindow.onbeforeunload)
      } catch (error) {
        console.error('检测 Jupyter dirty 状态时出错:', error)
        console.error('错误详情:', error.message)
      }
    }
  }
}
</script>

<template>
  <div class="jupyterhub-container">
    <div v-if="!showIframe" class="content-wrapper">
      <div class="logo-container">
        <img src="https://jupyter.org/assets/homepage/main-logo.svg" alt="Jupyter Logo" class="logo" />
      </div>
      <h1 class="title">JupyterHub 交互式实验平台</h1>
      <p class="subtitle">创建、共享和探索交互式计算环境</p>

      <el-button
        type="primary"
        size="large"
        class="action-button"
        @click="toggleIframe"
      >
        <span class="button-content">
          <i class="el-icon-right"></i>
          开始实验
        </span>
      </el-button>

      <div class="features">
        <div class="feature-item">
          <i class="el-icon-cpu"></i>
          <h3>强大的计算能力</h3>
          <p>支持多种编程语言和数据分析工具</p>
        </div>
        <div class="feature-item">
          <i class="el-icon-share"></i>
          <h3>协作共享</h3>
          <p>轻松与团队成员共享笔记本和成果</p>
        </div>
        <div class="feature-item">
          <i class="el-icon-cloudy"></i>
          <h3>云端访问</h3>
          <p>随时随地通过浏览器访问您的工作</p>
        </div>
      </div>
    </div>

    <div v-if="showIframe" class="iframe-container">
      <iframe
        ref="jupyterIframe"
        :src="jupyterUrl"
        frameborder="0"
        class="jupyter-iframe"
        allowfullscreen
      ></iframe>
      <el-button
        type="primary"
        size="large"
        class="back-button"
        @click="toggleIframe"
      >
        <span class="button-content">
          <i class="el-icon-back"></i>
          返回首页
        </span>
      </el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.jupyterhub-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.content-wrapper {
  max-width: 800px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 40px;
  text-align: center;
}

.iframe-container {
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  background: white;
  display: flex;
  flex-direction: column;
}

.jupyter-iframe {
  flex: 1;
  width: 100%;
  height: calc(100vh - 60px);
}

.back-button {
  margin: 10px;
  align-self: flex-start;
}

.logo-container {
  margin-bottom: 20px;
}

.logo {
  height: 80px;
  width: auto;
}

.title {
  font-size: 2.2rem;
  color: #2c3e50;
  margin-bottom: 10px;
  font-weight: 600;
}

.subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin-bottom: 30px;
}

.action-button {
  padding: 12px 30px;
  font-size: 1.1rem;
  border-radius: 8px;
  margin-bottom: 40px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(66, 165, 245, 0.4);
  }
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;

  i {
    margin-right: 8px;
  }
}

.features {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-top: 20px;
}

.feature-item {
  flex: 1;
  min-width: 200px;
  padding: 20px;
  margin: 10px;
  background: #f8f9fa;
  border-radius: 10px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  i {
    font-size: 2.5rem;
    color: #42a5f5;
    margin-bottom: 15px;
  }

  h3 {
    color: #2c3e50;
    margin-bottom: 10px;
  }

  p {
    color: #7f8c8d;
    font-size: 0.9rem;
  }
}

@media (max-width: 768px) {
  .content-wrapper {
    padding: 30px 20px;
  }

  .features {
    flex-direction: column;
  }

  .title {
    font-size: 1.8rem;
  }

  .subtitle {
    font-size: 1rem;
  }
}
</style>
