<template>
  <NormalDialog
    v-if="dialogShow"
    width="100%"
    title="图片预览"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="single-img-main">
      <viewer v-if="imgSrc" ref="viewer" :options="options" :images="[{src: imgSrc, alt: imgInfo}]" class="full-viewer" @inited="inited">
        <template #default="scope">
          <img v-for="item in scope.images" :key="item.src" style="width: 0;height: 0;" :src="item.src" class="img-responsive" />
        </template>
      </viewer>
    </div>
  </NormalDialog>
</template>

<script>
import 'viewerjs/dist/viewer.css'
import VueViewer from 'v-viewer'
import Vue from 'vue'
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
Vue.use(<PERSON>ueViewer)
VueViewer.setDefaults({
  title: (image) => image.alt || ''
})
export default {
  components: {
    NormalDialog
  },
  props: {
    src: {
      type: String,
      default: ''
    },
    info: {
      type: String,
      default: '图片预览'
    }
  },
  data () {
    return {
      dialogShow: false,
      imgSrc: '',
      imgInfo: '',
      $viewer: null,
      options: {
        scalable: false,
        toolbar: false,
        inline: true,
        title: false,
        url: 'src',
        show: () => {

        },
        hide: () => {

        }
      }
    }
  },
  methods: {
    inited (viewer) {
      this.$viewer = viewer
    },
    close () {
      this.dialogShow = false
      this.imgSrc = ''
      this.imgInfo = ''
    },
    open (src, info = '图片预览') {
      this.imgSrc = src
      this.imgInfo = info
      this.dialogShow = true
    }
  }
}
</script>

<style lang="scss" scoped>
.single-img-main {
  width: 100%;
  height: 80vh;
  position: relative;
  .full-viewer {
    width: 100%;
    height: 100%;
  }
}
::v-deep .viewer-canvas > img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}
</style>
