<template>
  <transition name="fade">
    <div
      v-show="showMenu"
      :style="{ top: position.top + 'px', left: position.left + 'px' }"
      class="dig-noteContent"
    >
      <div v-for="(item, index) in itemList" :key="index" class="item" @mousedown="clear" @click.stop="navFun(index)">
        <img :src="item.src" alt="" />
        <p>{{ item.title }}</p>
      </div>
      <!-- <div class="color_select">
        <div v-for="item in color " :key="item" :style="{backgroundColor:item}" class="color"></div>
      </div> -->
      <div v-if="showPreview" ref='scrollContainer' class="ai-preview" v-html="renderedHtml">

      </div>
    </div>
  </transition>
</template>

<script>
import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'
import mk from 'markdown-it-katex'
export default {
  name: 'NoteMenu',
  props: {
    position: {
      type: Object,
      default: null
    },
    selectStr: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      bookId: '',
      renderedHtml: '',
      isWorking: false,
      showPreview: false,
      showMenu: false,
      color: ['#FBEC7F', '#B6D8FB', '#CAEC82', '#F4B4CA', '#D2B3FA'],
      itemList: [
        {
          title: '解读',
          src: require('@/assets/digitalbooks/noteNav/ai.svg')
        },
        {
          title: '高亮',
          src: require('@/assets/digitalbooks/noteNav/heigh_light.svg')
        },
      // {
      //   title: '选色',
      //   src: require('@/assets/digitalbooks/noteNav/setColor.svg')
      // },
      {
        title: '笔记',
        src: require('@/assets/digitalbooks/noteNav/note.svg')
      }, {
        title: '复制',
        src: require('@/assets/digitalbooks/noteNav/copy.svg')
      }
      // {
      //   title: '百科',
      //   src: require('@/assets/digitalbooks/noteNav/word.svg')
      // }
      ],
      md: new MarkdownIt({
        html: true,
        breaks: false,
        linkify: true,
        typographer: true,
        tables: true,
        highlight: function (str, lang) {
          if (!lang) lang = 'plaintext'
          if (hljs.getLanguage(lang)) {
            try {
              return '<pre class="hljs"><div class="code-header">' +
                '<span class="code-lang">' + lang + '</span>' +
                '<button class="copy-btn" onclick="copyCode(this)">复制</button>' +
                '</div><code class="' + lang + '">' +
                hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +
                '</code></pre>'
            } catch (__) {
              return '<pre class="hljs"><code>' + this.md.utils.escapeHtml(str) + '</code></pre>'
            }
          }
          return '<pre class="hljs"><code>' + this.md.utils.escapeHtml(str) + '</code></pre>'
        }
      }).use(mk, {
        throwOnError: false,
        trust: true,
        macros: {
          '\\sum': '\\sum\\limits',
          '\\f': '\\frac'
        },
        delimiters: [
          { left: '$$', right: '$$', display: true },
          { left: '\\[', right: '\\]', display: true },
          { left: '$', right: '$', display: false },
          { left: '\\(', right: '\\)', display: false }
        ]
      }),
    }
  },
  methods: {
    show () {
      if (this.$route.query.id) {
        this.bookId = this.$route.query && this.$route.query.id
      }
      this.showMenu = true
    },
    hide () {
      if (!this.isWorking) {
        console.log('隐藏菜单')
        this.showPreview = false
        this.showMenu = false
      }
    },
    clear (e) {
      e.preventDefault()
    },
    navFun (index) {
      switch (index) {
        case 0 :
          if (!this.isWorking){
            this.isWorking = true
            this.getPreview()
          }
          break
        case 1 :
          // this.showPreview = false
          this.$emit('heighLight'); break
        case 2 :
          // this.showPreview = false
          this.$emit('addNote'); break
        case 3 :
          // this.showPreview = false
          this.$emit('copyText'); break
        // case 3 :
        //   this.$emit('baidu'); break
      }
    },
    async getPreview() {
      try {
        this.renderedHtml = '<p>思考中...</p>'
        this.showPreview = true
        const message = `结合上下文解读："${window.getSelection().toString()}"这段话在知识库中的含义，言简意赅，字数不超过300，不做任何推荐，不返回其他内容`
        const url = `${process.env.VUE_APP_BASE_API}/api/v2/comm/vt/aiChat?question=${message}&dataId=${this.bookId}&requestId=''&dataType=DIGITAL_BOOK`
        const response = await fetch(url)
        if (!response.ok) throw new Error('Network response was not ok')
        const reader = response.body.getReader()
        const textDecoder = new TextDecoder()
        let result = true
        let res = ''
        while (result) {
          const { done, value } = await reader.read()
          if (done) {
            result = false
            this.isWorking = false
            this.scrollToTop()
            break
          }
          const chunkText = textDecoder.decode(value).replace(/}{"code"/g, '}-down-{"code"')
          const arr = chunkText.split('-down-')
          arr.forEach(item => {
            if (this.isJSON(item)) {
              if (JSON.parse(item).data.result) {
                res += JSON.parse(item).data.result
                this.requestId = JSON.parse(item).data.id
              }
            }
          })
          this.renderedHtml = this.renderMarkdown(res)
          this.scrollToBottom()
        }
      } catch (e) {
        this.isWorking = false
        console.log(e)
      }
    },
    isJSON (str) {
      if (typeof str === 'string') {
        try {
          JSON.parse(str)
          return true
        } catch (e) {
          return false
        }
      }
      return false
    },
    renderMarkdown (text) {
      text = text.replace(/&lt;/g, '<').replace(/&gt;/g, '>')
      let html = this.md.render(text)
      html = html.replace(/>\s+</g, '><')
      html = html.replace(/<\/td>\s+<td>/g, '</td><td>')
      html = html.replace(/<\/th>\s+<th>/g, '</th><th>')
      html = html.replace(/<\/tr>\s+<tr>/g, '</tr><tr>')
      return html
    },
    // 滚动到最底部的方法
    scrollToBottom() {
      // 使用 $nextTick 确保 DOM 已更新
      this.$nextTick(() => {
        const container = this.$refs.scrollContainer
        if (container) {
          // 设置滚动条位置到最底部
          container.scrollTop = container.scrollHeight
        }
      })
    },
    // 滚动到最顶部的方法
    scrollToTop() {
      this.$nextTick(() => {
        const container = this.$refs.scrollContainer
        if (container) {
          // 设置滚动条位置到最顶部
          container.scrollTop = 0
        }
      })
    }
  }

}
</script>

<style scoped lang='scss'>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.25s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active in <2.1.8 */ {
  opacity: 0;
}

.dig-noteContent {
  display: flex;
  padding: 12px 0px;
  position: fixed;
  z-index: 1000;
  flex-direction: column;
  align-items: flex-end;
  // gap: 10px;
  border-radius: 10px;
  border: 1px solid #f2f2f2;
  background: #fff;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  width: 91px;
  height: 190px;
  .item {
    width: 100%;
    height: 42px;
    text-align: center;
    display: flex;
    justify-content:center;
    gap:5px;
    cursor: pointer;
    img{
      width: 22px;
    }
    p{
      white-space:nowrap;
      margin-top: 13px;
      font-size: 14px;
    }
  }
  .item:hover{
    background-color: #e8f4ff;
    color: #46a6ff;
}
.color_select{
  width:36px;
  height:136px;
  border-radius: 3px;
  background: #fff;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  position: absolute;
  right:-60px;
  padding:10px;
  .color{
    width: 15px;
    height:15px;
    border-radius: 7.5px;
    margin-top: 7px;
  }
}
  .ai-preview{
    position: absolute;
    width: 375px;
    height: 250px;
    background-color: white;
    border-radius: 10px;
    border: 1px solid rgba(215, 215, 215, 1);
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
    right: 60px;
    top: -25px;
    overflow-y: auto;
    padding: 5px 15px;
    transform: scale(0.8);
    ::v-deep table {
      border-collapse: collapse;
    }
    ::v-deep th {
      border: 1px solid #bbb;
    }
    ::v-deep td {
      border: 1px solid #bbb;
    }
  }
}
</style>
