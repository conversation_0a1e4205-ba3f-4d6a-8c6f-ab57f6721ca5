<template>
  <div v-if="show" class="editor-dig">
    <div class="head-box">
      <div class="left">
        <img
          class="back"
          src="@/assets/digitalbooks/arrow-left.svg"
          @click="back"
        />
        <div class="head-title article-singer-container">
          {{ bookInfo.title }}
        </div>
      </div>
      <div class="right">
        <div class="resource_info_des">
          <img src="../../../../assets/digitalbooks/resource_tag.png" alt="" />
          <p>知识点：<span>{{ resourceData&&resourceData.knowlageQuantity||0 }}</span></p>
        </div>
        <div class="resource_info">
          <p>教材资源：<span>{{ resourceData&&resourceData.multimediaQuantity }}</span></p>
          <p>实训数：<span>{{ resourceData&&resourceData.trainingQuantity||0 }}</span></p>
          <p>习题：<span>{{ resourceData&&resourceData.questionQuantity||0 }}</span></p>
          <p>任务数：<span>{{ resourceData&&resourceData.taskQuantity||0 }}</span></p>
          <p>交互案例：<span>{{ resourceData&&resourceData.caseQuantity ||0 }}</span></p>
        </div>
      </div>
    </div>
    <div v-if="!hasKnowledge" class="content-box-empty">
      <ReadEmpty msg='本书暂无知识图谱' str-color='white'/>
    </div>
    <div v-else class="content-box">
      <img v-show="!showCatalogue" class="show_catalogue" src="../../../../assets/digitalbooks/expendend.svg" alt="" @click="showCatalogue=true" />
      <div v-show="showCatalogue" class="card-left">
        <div class="top"><p>目录</p> <img src="../../../../assets/digitalbooks/close.svg" alt="" @click="showCatalogue=false" /></div>
        <div
          class="card-title"
          :class="activeId === 0 ? 'active' : ''"
          @click="changeData(0)"
        >
          <span v-if="!hasBook"><i class="el-icon-lock"></i></span>
          全部章节
        </div>
        <div
          v-for="(item, index) in catalogueList"
          :key="index"
          class="card-title"
          :class="activeId === item.id ? 'active' : ''"
          :title="item.title"
          @click="changeData(item.id)"
        >
          <span v-if="!hasBook&&index!==0&&index!==1"><i class="el-icon-lock"></i></span>
          {{ item.title }}
        </div>
      </div>
      <div class="card-right">
        <div id="chart" class="editor-content-view">
          <Chart ref="chart" :show-detail="showDetail" :chart-data="chartData" :active-id="activeId" @getHtml="getHtml" />
          <template v-if="chartData&&chartData.nodes.length===0">
            <div class="w h flex items-center justify-center maskChart">
              <div class="flex flex-col items-center" style="color: #ffffff">
                <img
                  class="empty"
                  src="@/assets/images/empty.png"
                  alt="占位图2"
                />
                暂无数据
              </div>
            </div>
          </template>
        </div>
      </div>
      <div v-show="showDetail" class="detail_info">
        <img class="close_detail" src="../../../../assets/digitalbooks/read/detail_close.png" alt="" @click="closeDetail" />
        <div class="title">{{ title }}</div>
        <div
          v-if="html.length!==0"
          id="konwledgeContent"
          ref="richContext"
          class="editor-content-view"
          style="padding-top: 50px; padding-left: 10px;padding-right: 10px;height: 85%;margin-top: 15%;"
          @click="showImg"
          v-html="html"
        ></div>
        <div class="button_group">
          <div v-if="linkId.length!==0" class="detail" @click="toRead()">内容详情</div>
          <img v-if="bookConfig&&bookConfig.aiMateUrl" src="../../../../assets/digitalbooks/ai_group.png" alt="" @click="showAi" />
        </div>
        <template v-if="html.length===0">
          <div class="w h flex items-center justify-center">
            <div class="flex flex-col items-center" style="color: #ffffff">
              <img
                class="empty"
                src="@/assets/images/empty.png"
                alt="占位图2"
              />
              暂无数据
            </div>
          </div>
        </template>
      </div>
    </div>
    <showImg ref="imgPop" :src="imgSrc" />
    <PayToast ref="pay" :good-info="bookInfo&&bookInfo.goodsComm" />
    <aiChat ref="chatDrawer" :book-config="bookConfig" />
    <readDrawer :id="linkId" ref="readDrawer" />
  </div>
</template>

<script>
import ReadEmpty from '@/components/classPro/Empty/readEmpty'
import Chart from './chart'
import axios from 'axios'
import showImg from './showImg.vue'
import PayToast from '@/components/classPro/Pay/index.vue'
import aiChat from './aiChat.vue'
import readDrawer from './readDrawer.vue'
import { getDigitalCatalogueData } from '@/api/digital-api.js'
export default {
  components: {
    Chart,
    showImg,
    PayToast,
    aiChat,
    readDrawer,
    ReadEmpty
  },
  props: {
    bookInfo: {
      type: Object,
      default: null
    },
    bookId: {
      type: String,
      default: '0'
    },
    catalogueList: {
      type: Array,
      default: null
    },
    currChapter: {
      type: Object,
      default: null
    },
    bookConfig: {
      type: Object,
      default: null
    },
    hasKnowledge: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      show: false,
      html: '',
      title: '',
      activeId: 0,
      getUrl: process.env.VUE_APP_ADMIN_API + '/Home/api/echartForceGraphData',
      getHtmlUrl: process.env.VUE_APP_ADMIN_API + '/Home/api/echartForceGraphNodeInfo',
      chartData: null,
      hasBook: false,
      imgSrc: '',
      showDetail: false,
      showCatalogue: true,
      resourceData: null,
      linkId: []
    }
  },
  watch: {
    activeId: {
      handler: function (val) {
        this.$nextTick(async () => {
          const { data } = await getDigitalCatalogueData({
            bookId: this.bookId,
            catalogueId: val || null
          })
          this.resourceData = data
        })
      },
      immediate: true
    }
  },
  async mounted () {

  },
  beforeDestroy () {
  },
  methods: {
    toRead() {
      this.$refs.readDrawer.open()
    },
    showAi() {
      this.$refs.chatDrawer.open()
    },
    closeDetail() {
      this.showDetail = false
    },
    showImg (e) {
      if (e.target.nodeName === 'IMG') {
        this.imgSrc = e.target.currentSrc
        this.$refs.imgPop.open()
      }
    },
    async getHtml (id) {
      const { data } = await axios.get(this.getHtmlUrl, {
        params: {
          id: id
        }
      })
      this.html = data.content.discrption ? data.content.discrption : ''
      this.title = data.content.name
      this.linkId = data.content.catalogue_ids === '' ? [] : data.content.catalogue_ids.split(',')
      this.showDetail = true
      this.$nextTick(() => {
        this.setVideo()
        window.MathJax.typesetPromise()
      })
    },
    open () {
      this.show = true
      this.activeId = this.findCurrChapter()
      if (this.bookInfo.studentCourseId) {
        this.hasBook = true
      } else {
        this.hasBook = false
      }
      this.getData()
    },
    changeData (val) {
      if (!this.hasBook && (val !== this.catalogueList[0].id && val !== this.catalogueList[1].id)) {
        this.$message.warning('您未兑换，暂无权限阅读!')
        this.$refs.pay.show()
        return
      }
      this.activeId = val
      this.getData()
    },
    async getData () {
      if (!this.hasBook && (this.activeId !== this.catalogueList[0].id && this.activeId !== this.catalogueList[1].id)) {
        this.activeId = this.catalogueList[0].id
      }
      const { data } = await axios.get(this.getUrl, {
        params: {
          book_id: this.bookId,
          catalogue_id: this.activeId,
          knowledge_type: this.activeId === 0 ? 'DIGITAL_BOOK' : 'DIGITAL_BOOK_CHAPTER'
        }

      })
      this.chartData = data.content
      this.$nextTick(() => {
        this.$refs.chart.renderChart()
        this.html = ''
      })
    },
    setVideo () {
      const videoList = document.getElementsByTagName('video')
      for (let i = 0; i < videoList.length; i++) {
        videoList[i].setAttribute('controlslist', 'nodownload noplaybackrate')
        videoList[i].setAttribute('disablePictureInPicture', true)
      }
      const audioList = document.getElementsByTagName('audio')
      for (let i = 0; i < audioList.length; i++) {
        audioList[i].setAttribute('controlslist', 'nodownload noplaybackrate')
        // audioList[i].setAttribute('disablePictureInPicture', true)
      }
    },
    findCurrChapter () {
      if (!this.currChapter) {
        return 0
      }
      const chapter = this.catalogueList.find(item => item.id === this.currChapter.id)
      return chapter ? chapter.id : this.currChapter.parentId
    },
    back () {
      this.show = false
      if (this.$route.query && this.$route.query.to === 'graph') {
        this.$router.go(-1)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.editor-dig {
  width: 100%;
  height: 100%;
  padding: 10px;
  position: fixed !important;
  left: 0 !important;
  top: 0 !important;
  z-index: 1999;
  background: url('../../../../assets/digitalbooks//read/bg.png') no-repeat center/cover;
  .head-box {
    height: 40px;
    min-width: 1040px;
    width: 98%;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    position: relative;
    .left{
      display: flex;
      align-items: center;
    }
    .right{
      display: flex;
    }
    .resource_info{
      width: 460px;
      height: 40px;
      background: rgb(255,255,255,0.10);
      border: 1px solid rgb(255,255,255,0.22);
      border-radius: 6px;
      display: flex;
      justify-content: space-between;
      padding-left: 5px;
      padding-right: 5px;
      align-items: center;
      color: #ffffff;
      p{
        margin-left: 5px;
        font-weight: 400;
        white-space: nowrap;
        font-size: 14px;
        span{
          font-weight: 600;
        }
      }
    }
    .resource_info_des{
      width: 148px;
      height: 40px;
      background: rgb(255,255,255,0.10);
      border: 1px solid rgb(255,255,255,0.22);
      border-radius: 6px;
      margin-right: 10px;
      display: flex;
      align-items: center;
      color: #ffffff;
      padding-left: 5px;
      padding-right: 5px;
      img{
        width: 25px;
        margin-left: 5px;
      }
      p{
        font-weight: 400;
        font-size: 14px;
        white-space: nowrap;
        margin-left: 10px;
        span{
          font-weight: 600;
        }
      }
    }
    .share {
      position: absolute;
      right: 40px;
      font-size: 12px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      img {
        width: 20px;
        height: 20px;
      }
    }

    .back {
      width: 30px;
      height: 30px;
      object-fit: cover;
      cursor: pointer;
    }

    .head-title {
      color:#ffffff;
      font-size: 16px;
      font-weight: 500;
      margin-left: 10px;
    }
  }
  .content-box-empty{
    width: 98%;
    height: calc(100% - 40px - 10px);
    margin: 0 auto;
    border-radius: 10px;
    border: 2px solid rgb(255,255,255,0.22);
    position: relative;
  }
  .content-box {
    width: 98%;
    height: calc(100% - 40px - 10px);
    margin: 0 auto;
    border-radius: 10px;
    border: 2px solid rgb(255,255,255,0.22);
    position: relative;
    .show_catalogue{
      position: absolute;
      left: 10px;
      top:10px;
      z-index: 1;
      cursor: pointer;
    }
    .card-left {
      position: absolute;
      width: 15%;
      height: 100%;
      padding: 10px;
      flex-direction: column;
      flex-shrink: 0;
      border-radius: 10px;
      overflow: auto;
      z-index: 1;
      background: rgb(10, 43, 98, 0.898);
      border-right: 2px solid rgb(255,255,255,0.22);
      color: #ffffff;
      @include scrollBar;
      .top{
        display: flex;
        justify-content: space-between;
        padding-left: 10px;
        padding-right: 10px;
        img{
          cursor: pointer;
        }
      }
      .card-title {
        width: 90%;
        height: 40px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 14px;
        text-align: left;
        margin: 0 auto;
        cursor: pointer;
        padding-left: 5px;
        line-height: 40px;
        margin-top: 5px;
      }
      .card-title:hover {
        background: #619eee;
      }
      .active {
        background: #2F80ED;
      }
      .mulu {
        color: #ffffff;
        font-size: 16px;
        font-weight: 500;
      }
      .show-btn {
        cursor: pointer;
        i {
          font-size: 20px;
        }
      }
    }
    .card-right {
      width: 100%;
      height: 100%;
      position: relative;
      padding: 0 !important;
      overflow: hidden;
      .maskChart{
        position: absolute;
        left: 0;
        top:0;
      }
    }

    .card-right-all {
      // width: calc(100% - 10px);
      width: 793.667px;
    }
    .detail_info {
      width: 20%;
      height: 100%;
      padding: 10px;
      flex-direction: column;
      flex-shrink: 0;
      border-radius: 10px;
      position: absolute;
      right: 0px;
      top:0px;
      background: rgb(10, 43, 98, 0.898);
      border-left: 2px solid rgb(255,255,255,0.22);
      padding-top: 50px;
      .button_group{
        width: 100%;
        height: 9%;
        position: absolute;
        bottom: 0;
        left: 0;
        z-index: 1;
        display: flex;
        justify-content: space-between;
        padding: 20px;
        .detail{
          width: 76px;
          height: 40px;
          background: #2F80ED;
          color: #fff;
          text-align: center;
          line-height: 40px;
          border-radius: 5px;
          font-size: 14px;
          cursor: pointer;
        }
        img{
          height: 40px;
          cursor: pointer;
        }
      }
      .title{
        font-size: 20px;
        font-weight: bold;
        color: #ffffff;
        width: 80%;
        left: 10%;
        top:10px;
        text-align: center;
        position: absolute;
      }
      .close_detail{
        width: 20px;
        position: absolute;
        right: 10px;
        top:10px;
        cursor: pointer;
        z-index: 10;
      }
    }
    .card-right,
    .card-right-all {
      display: flex;
      padding: 40px;
      flex-direction: column;
      flex-shrink: 0;
      border-radius: 10px;
      overflow-y: auto;
      @include scrollBar;
      // line-height: 20px;
    }

    .empty {
      width: 100px;
      height: 100px;
      margin-bottom: 20px;
    }
  }

  .editor-content-view {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top:0;
    overflow-y: auto;
    @include scrollBar;
    color: #ffffff !important;
    ::v-deep img {
      max-width: 100% !important;
      height: auto !important;
      cursor: pointer;
    }
    ::v-deep video {
      width: 100%;
    }
    ::v-deep p,
    ::v-deep li {
      white-space: pre-wrap;
      /* 保留空格 */
    }

    ::v-deep blockquote {
      border-left: 8px solid #d0e5f2;
      padding: 10px 10px;
      margin: 10px 0;
      background-color: #f1f1f1;
    }

    ::v-deep table {
      border-collapse: collapse;
    }

    ::v-deep th,
    ::v-deep td {
      border: 1px solid #ccc;
      min-width: 50px;
      height: 20px;
    }

    ::v-deep th {
      background-color: #f1f1f1;
    }

    ::v-deep code {
      font-family: monospace;
      background-color: #eee;
      padding: 3px;
      border-radius: 3px;
    }

    ::v-deep pre > code {
      display: block;
      padding: 10px;
    }

    ::v-deep input[type='checkbox'] {
      margin-right: 5px;
    }

    ::v-deep ul {
      display: block;
      list-style-type: disc;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0px;
      margin-inline-end: 0px;
      padding-inline-start: 40px;
    }
    ::v-deep ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0px;
      margin-inline-end: 0px;
      padding-inline-start: 40px;
    }
  }
}
</style>
