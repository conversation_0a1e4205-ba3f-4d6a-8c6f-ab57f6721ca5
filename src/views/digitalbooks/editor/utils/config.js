// 引入主题和图标信息
import 'tinymce/themes/silver/theme.min.js'
import '../../../../../public/tinymce/icons/default/icons'
import tinymce from 'tinymce/tinymce'
// 引入插件
// https://www.tiny.cloud/docs/plugins/
import 'tinymce/plugins/lists'// 列表插件
import 'tinymce/plugins/code' // 源代码插件
import 'tinymce/plugins/pagebreak' // 分页符插件
import 'tinymce/plugins/charmap' // 特殊符号插件
import 'tinymce/plugins/emoticons' // 表情插件
import 'tinymce/plugins/save' // 保存插件
import 'tinymce/plugins/preview' // 预览插件
// import 'tinymce/plugins/print' // 打印
import 'tinymce/plugins/image'// 上传图片插件
// import 'tinymce/plugins/media'// 视频插件
import 'tinymce/plugins/link' // 链接插件
import 'tinymce/plugins/anchor' // 锚点插件
import 'tinymce/plugins/codesample' // 代码插件
import 'tinymce/plugins/table'// 表格插件
import 'tinymce/plugins/searchreplace' // 查找、替换插件
// import 'tinymce/plugins/hr' // 水平分割线插件
import 'tinymce/plugins/insertdatetime' // 时间日期插件
import 'tinymce/plugins/wordcount'// 字数统计插件
import 'tinymce/plugins/fullscreen' // 全屏插件
import 'tinymce/plugins/help' // 帮助插件
import 'tinymce/plugins/noneditable'
import './trainingListbox'
import './addColorBlock'
import './imageEditor'
import 'tinymce/plugins/autoresize'
import 'tinymce-paragraphspacing'
import { getFileUploadAuthor } from '@/api/user-api'
import axios from 'axios'
import { Notification, Message } from 'element-ui'
import { throttle } from '@/utils/index'
import './formatPainter'
import './ui' // 引入自定义图标
// https://www.tiny.cloud/docs/configure/integration-and-setup/
export default {
  selector: '#tinymce',
  external_plugins: {
    'powerpaste': '/tinymce/powerpaste/plugin.min.js',
    'kityformula-editor': '/tinymce/kityformula-editor/plugin.js',
    'mathjax': '/tinymce/tinymce-mathjax/plugin.js'
  },
  mathjax: { lib: '/tex-mml-svg.js' },
  /**
   * 语言路径
   */
  language_url: '/tinymce/langs/zh_CN.js',
  fontsize_formats: '8pt 10pt 12pt 14pt 16pt 18pt 24pt 28pt 32pt 36pt 40pt 44pt 48pt 54pt 60pt 66pt 72pt 80pt 88pt 96pt',
  base_url: '/',
  /**
   * 语言
   */
  language: 'zh_CN',

  /**
   * 主题样式路径
   */
  skin_url: '/tinymce/skins/ui/oxide1',

  /**
   * 文本样式路径
   */
  content_css: '/tinymce/skins/content/default/content.css',
  content_style: `
    .mceNonEditable {
      text-indent: 0;
      text-align:left;
    }
    body{
      font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif !important;
    }
    .draggable-div {
                    transition: box-shadow 0.2s;
                    border: 0
                  }
                  .draggable-div-selected {
                    border: 2px solid #3498db;
                    box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
                  }
                  .resize-handle {
                    width: 12px;
                    height: 12px;
                    background-color: #3498db;
                    border-radius: 3px;
                    position: absolute;
                    bottom: 0;
                    right: 0;
                    z-index: 20;
                    cursor: se-resize;
                    display: none
                  }
                  .move-handle {
                    width: 12px;
                    height: 12px;
                    background-color: #3498db;
                    border-radius: 3px;
                    position: absolute;
                    top: 0;
                    left: 0;
                    z-index: 20;
                    cursor: move;
                    display: none
                  }
  `,
  /**
   * 表情路径
   */
  // emoticons_database_url: '/public/tinymce/emojis/emojis.min.js',
  /**
   * 字体
   */
  font_formats: '宋体=songti;黑体=heiti;仿宋=fangsong;微软雅黑=yahei;楷体=kaiti;思源宋体=siyuansongti;思源黑体=siyuanheiti',
  /**
   * 宽度
   */
  width: '100%',

  /**
   * 高度
   */
  height: '100%',

  /**
   * 插件
   */
  plugins: 'customIcons lists code pagebreak charmap  save preview  uploadImg  link tips noneditable indent2em lineHeight paragraphspacing ' +
           'anchor codesample table wordcount fullscreen help searchreplace  insertdatetime ' +
           'uploadAudio uploadVideo imgGroup uploadVideoCard fileDownLoad formateImg powerpaste kityformula-editor mathjax test customIframe trainingdPlugin excelTrain aiTraining pythonTraining addCase trainingListbox colorBlock autoresize formatpainter imageEditor',
  /**
   * 菜单栏
   * file 文件
   * edit 编辑
   * view 视图
   * insert 插入
   * format 格式
   * tools 工具
   * table 表格
   * help 帮助
   */
  menubar: '',
  /**
   * 工具栏
   * https://www.tiny.cloud/docs/demo/full-featured/
   * | formatselect fontselect fontsizeselect | 段落、字体、字号
   * | undo redo | 撤销、重做
   * | code bold italic underline strikethrough | 源代码、粗体、斜体、下划线、删除线
   * | alignleft aligncenter alignright alignjustify | 左对齐、中间对齐、右对齐、两端对齐
   * | outdent indent numlist bullist insertdatetime | 减少缩进、增加缩进、编号列表、项目列表、时间日期
   * | table forecolor backcolor removeformat | 表格、文字颜色、背景色、清除格式
   * | hr searchreplace pagebreak charmap emoticons | 水平分割线、查找替换、分页符、特殊符号、表情
   * | fullscreen preview save print | 全屏、预览、保存、打印
   * | image media link anchor codesample | 上传文件、上传素材、插入链接、锚点、插入代码
   */
  lineheight_formats: '1 1.5 2 2.5 3 3.5 4 4.5 5 5.5 6 6.5 7 7.5 8 8.5 9 9.5 10',
  paragraphspacing: '0 15pt 30pt ',
  paragraphspacing_options: {
    fillStyle: 'inherit',
    strokeStyle: 'inherit',
    strokeWidth: 'inherit'
  },
  toolbar_groups: {
    formatting: {
      icon: 'formatterText',
      tooltip: '文字格式',
      items: 'formatpainter bold italic underline strikethrough  superscript subscript'
    },
    alinglign: {
      icon: 'formatterAlign',
      tooltip: '对齐方式',
      items: 'alignleft aligncenter alignright alignjustify'
    },
    indentGroup: {
      icon: 'indenttag',
      tooltip: '缩进',
      items: 'outdent indent numlist bullist   indent2em'
    }
    // trainingGroup: {
    //   text: '',
    //   icon: 'addTraining',
    //   tooltip: '实训',
    //   items: 'trainingdPlugin excelTrain'
    // }
  },
  toolbar1: '| formatselect fontselect fontsizeselect ' +
           ' undo redo  lineHeight wordcount paragraphspacing  removeformat  formatting  alinglign indentGroup  table forecolor backcolor  ' +
           ' searchreplace  charmap emoticons kityformula-editor ' +
           'codesample tips  uploadImg  uploadVideo uploadAudio imgGroup uploadVideoCard fileDownLoad formateImg test customIframe trainingListbox colorBlock autoresize imageEditor addCase',

  /**
   * 工具栏展开方式
   */
  // toolbar_mode: 'floating',
  // toolbar_location: 'right',
  statusbar: false,
  /**
   * 格式化工具
   */
  style_formats: [
    {
      title: '首行缩进',
      block: 'p',
      styles: {
        'text-indent': '2em'
      }
    }
  ],
  // extended_valid_elements: 'span[class|style|data-*],mjx-container[class|style|jax|tabindex|ctxtmenu_counter],mjx-math[class|aria-hidden],mjx-msub,mjx-script,mjx-texatom[class|size|texclass],mjx-mn[class],mjx-c[class]',
  // custom_elements: 'mjx-container,mjx-math,mjx-msub,mjx-script,mjx-texatom,mjx-mn,mjx-c',
  // init_instance_callback: function (editor) {
  //   // 重新渲染 MathJax 公式
  //   window.MathJax.typesetPromise()
  // },
  /**
   * 是否允许拖动
   * true（仅允许改变高度）, false（完全不让你动）, 'both'（宽高都能改变，注意引号）
   */
  resize: true,

  /**
   * 底部状态栏
   */

  /**
   * 是否显示版权信息
   */
  branding: false,

  /**
   * 是否允许黏贴图片
   */
  paste_data_images: true,
  //    添加扩展插件

  powerpaste_word_import: 'merge', // 参数可以是propmt, merge, clear，效果自行切换对比
  powerpaste_html_import: 'merge', // propmt, merge, clear
  powerpaste_allow_local_images: true,
  /**
   * 时间日期格式化
   */
  insertdatetime_formats: ['%H点%M分', '%Y年%m月%d日', '%Y年%m月%d日 %H点%M分', '%Y-%m-%d %H:%M'],
  paste_preprocess: function(plugin, args) {
    if (args.content.indexOf('test_card') !== -1) {
      Message.warning('粘贴内容含有习题集，禁止粘贴')
      args.content = ''
      return
    }
    if (args.content.indexOf('mceNonEditable') !== -1) {
      Message.warning('粘贴内容含有图片集，视频集等功能，请确认其是否有效')
    }
  },
  // 默认使用base64格式
  urlconverter_callback: (url, node, onSave, name) => {
    if (node === 'img' && url.startsWith('blob:')) {
      console.log('urlConverter:', url, node, onSave, name)
      tinymce.activeEditor && tinymce.activeEditor.uploadImages()
    }
    // Return new URL
    return url
  },
  images_upload_handler: async function (blobInfo, success, failure) {
    const file = blobInfo.blob()
    const isLt2M = file.size / 1024 / 1024 < 1
    console.log(file.size, isLt2M)
    if (!isLt2M) {
      Message.warning('上传图片超过1M,可能会影响阅读体验，请压缩图片')
    }
    const { data } = await getFileUploadAuthor({
      mediaType: 'IMAGE',
      contentType: '',
      quantity: 1,
      fileName: file.name
    })
    const ossCDN = data[0].ossConfig.ossCDN
    try {
      const formData = new FormData()
      formData.append('success_action_status', '200')
      formData.append('callback', '')
      formData.append('key', data[0].fileName)
      formData.append('policy', data[0].policy)
      formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
      formData.append('signature', data[0].signature)
      formData.append('file', file)
      const name = file.name ? file.name : ''
      const notif = Notification({
        title: `${name}上传中`,
        message: '0%',
        duration: 0
      })
      await axios.post(data[0].ossConfig.host, formData, {
        onUploadProgress: (progress) => {
          const complete = Math.floor(progress.loaded / progress.total * 100)
          notif.message = complete + '%'
          console.log(complete)
          if (complete >= 100) {
            notif.close()
          }
        }
      })
      success(`${ossCDN}/${data[0].fileName}`, data[0].fileName, '')
    } catch (error) {
      console.log(error)
      failure('文件上传失败，请重试')
    }
  },
  init_instance_callback: function (editor) {
    editor.on('NodeChange', function (e) {
      if (e && e.element.nodeName.toLowerCase() === 'img') {
        const img = e.element
        const width = img.style.width
        const height = img.style.height
        img.removeAttribute('width')
        img.removeAttribute('height')
        // 如果宽度和高度是以像素为单位，则转换为百分比
        if (width.endsWith('px')) {
          const newWidth = (parseFloat(width) / editor.getBody().offsetWidth * 100) + '%'
          img.style.width = newWidth
          img.setAttribute('data-mce-style', `width: ${newWidth};`)
        }
        if (height.endsWith('px')) {
          const newHeight = (parseFloat(height) / editor.getBody().offsetHeight * 100) + '%'
          img.style.height = newHeight
          img.setAttribute('data-mce-style', `height: ${newHeight};`)
        }
      }
      if (e && e.element.nodeName.toLowerCase() === 'video') {
        const video = e.element
        const editorWidth = editor.getBody().offsetWidth
        if (video.getAttribute('width') === '100%') {
          return
        }
        const width = video.style.width || video.getAttribute('width') + 'px'
        console.log(width)
        // 确保宽度是有效的数值
        if (width && width.endsWith('px')) {
          const pixelWidth = parseFloat(width)
          const widthPercent = ((pixelWidth / editorWidth) * 100).toFixed(2) + '%'

          // 设置视频宽度为百分比
          video.setAttribute('width', widthPercent)
          video.style.width = widthPercent
          video.setAttribute('data-mce-style', `width: ${widthPercent};`)
          video.removeAttribute('height') // 可选：移除高度以保持比例
        }
      }
    })
    editor.on('selectionchange', function(e) {
      const selectedText = editor.selection.getContent() // 获取当前选中的内容
      if (selectedText) {
        const selection = editor.selection // 获取选区对象
        const content = selection.getContent({ format: 'html' }) // 获取选中的内容

        // 创建一个临时 DOM 容器来解析选区内容
        const container = document.createElement('div')
        container.innerHTML = content

        let hasNonEditable = false // 是否包含 mceNonEditable
        let hasOtherSiblings = false // 是否有其他同级元素

        // 遍历顶级子元素
        Array.from(container.childNodes).forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) { // 元素节点
            if (node.classList.contains('mceNonEditable')) {
              hasNonEditable = true // 有 mceNonEditable 类的元素
            } else if (node.querySelector('.mceNonEditable') !== null) {
              hasNonEditable = true
            } else {
              hasOtherSiblings = true // 有其他元素
            }
          } else if (node.nodeType === Node.TEXT_NODE && node.nodeValue.trim() !== '') {
            hasOtherSiblings = true // 有非空文本节点
          }
        })

        if (hasNonEditable && hasOtherSiblings) {
          throttle(function () {
            Message.warning('请不要选中视频块，图集，习题集等系统定义的块进行修改')
            selection.collapse() // 取消选中，将光标移动到选区开始
          }, 1000)
        }
      } else {
        // 没有选中文本
        console.log('No text selected')
      }
    })
  }
}

