import tinymce from 'tinymce/tinymce'
import { addVideoCard, closeVideoCard, setData } from '../components/videoCard'
import { MessageBox } from 'element-ui'
export function uploadVideoCard (editor, url) {
  // 添加悬浮 上下文工具栏
  editor.ui.registry.addContextToolbar('videocardeditcontrol', {
    // 浮层的触发条件
    predicate: function (node) {
      return node.classList.contains('video_card')
      // return node.nodeName.toLowerCase() === 'p'
    },
    items: 'videocardchangecontrol videocardremovecontrol', // 显示的工具列表
    position: 'selection' // 工具栏放置位置  selection node line
    // scope: 'editor'
  })
  // 浮层注册编辑和删除按钮
  editor.ui.registry.addButton('videocardchangecontrol', {
    icon: 'edit-block',
    tooltip: '编辑视频卡',
    onAction: () => {
      const bookmark = tinymce.activeEditor.selection.getBookmark()
      const selectContent = tinymce.activeEditor.selection.getNode()
      setData({
        postUrl: selectContent.getElementsByTagName('video')[0].poster,
        videoUrl: selectContent.getElementsByTagName('video')[0].src,
        title: selectContent.getElementsByTagName('p')[1].innerText
      })
      addVideoCard({
        onSubmit (data) {
          tinymce.activeEditor.selection.moveToBookmark(bookmark)
          const media = `<div class='video_card mceNonEditable' style='margin-bottom:2px;width:100%;background: #E3F5FF;border-radius: 10px;box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);overflow:hidden;padding:20px;box-sizing: border-box;'><video class="video_button" width="auto" height="auto"  controlsList="nodownload" style="width: 50%;height: 180px;border-radius:10px; float:right;display: block;"  controls="controls" src="${data.videoUrl}" poster="${data.postUrl}"></video> <div style='float:left;width:40%'><p style='font-weight:700;font-size:16px;'>视频</p><p style='color:#333333;font-size:14px;width:100%';margin-top:10px'>${data.title}</p></div></div>`
          tinymce.activeEditor.selection.setContent(media)
          closeVideoCard()
        }
      })
    }
  })
  editor.ui.registry.addButton('videocardremovecontrol', {
    icon: 'remove',
    tooltip: '删除视频卡',
    onAction: (editor) => {
      const bookmark = tinymce.activeEditor.selection.getBookmark()
      MessageBox.confirm('确认删除视频块？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        tinymce.activeEditor.selection.moveToBookmark(bookmark)
        tinymce.activeEditor.focus()
        tinymce.activeEditor.selection.setContent('')
        tinymce.activeEditor.selection.collapse()
      })
    }
  })
  return editor.ui.registry.addButton('uploadVideoCard', {
    icon: 'videoCard',
    tooltip: '插入视频卡',
    onAction: function () {
      let type = 'add'
      const selectContent = tinymce.activeEditor.selection.getNode()
      const bookmark = tinymce.activeEditor.selection.getBookmark()
      if (selectContent.classList.contains('video_card')) {
        setData({
          postUrl: selectContent.getElementsByTagName('video')[0].poster,
          videoUrl: selectContent.getElementsByTagName('video')[0].src,
          title: selectContent.getElementsByTagName('p')[1].innerText
        })
        type = 'edit'
      }
      addVideoCard({
        onSubmit (data) {
          if (type === 'add') {
            const media = `<div class='video_card mceNonEditable' style='margin-bottom:2px;width:100%;background: #E3F5FF;border-radius: 10px;box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);overflow:hidden;padding:20px;box-sizing: border-box;'><video class="video_button" width="auto" height="auto"  controlsList="nodownload" style="width: 50%;height: 180px;border-radius:10px; float:right;display: block;"  controls="controls" src="${data.videoUrl}" poster="${data.postUrl}"></video> <div style='float:left;width:40%'><p style='font-weight:700;font-size:16px;'>视频</p><p style='color:#333333;font-size:14px;width:100%';margin-top:10px'>${data.title}</p></div></div>`
            tinymce.activeEditor.selection.setContent(media)
            const content = editor.getContent()
            editor.setContent(content)
          }
          if (type === 'edit') {
            tinymce.activeEditor.selection.moveToBookmark(bookmark)
            const media = `<div class='video_card mceNonEditable' style='margin-bottom:2px;width:100%;background: #E3F5FF;border-radius: 10px;box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);overflow:hidden;padding:20px;box-sizing: border-box;'><video class="video_button" width="auto" height="auto"  controlsList="nodownload" style="width: 50%;height: 180px;border-radius:10px; float:right;display: block;"  controls="controls" src="${data.videoUrl}" poster="${data.postUrl}"></video> <div style='float:left;width:40%'><p style='font-weight:700;font-size:16px;'>视频</p><p style='color:#333333;font-size:14px;width:100%';margin-top:10px'>${data.title}</p></div></div>`
            tinymce.activeEditor.selection.setContent(media)
          }
          closeVideoCard()
        }

      })
    }
  })
}
