import tinymce from 'tinymce/tinymce'
import { addCaseModal, setData, closeCase} from '../components/addCase/addCase'
import { MessageBox } from 'element-ui'

export function addCase (editor) {
  editor.ui.registry.addContextToolbar('casecardeditcontrol', {
    predicate: function (node) {
      return node.classList.contains('case_card')
    },
    items: 'casecardchangecontrol casecardremovecontrol',
    position: 'selection'
  })
  // 浮层注册编辑和删除按钮
  editor.ui.registry.addButton('casecardchangecontrol', {
    icon: 'edit-block',
    tooltip: '编辑案例',
    onAction: () => {
      const bookmark = editor.selection.getBookmark()
      const selectContent = editor.selection.getNode()
      const oldData = JSON.parse(selectContent.getElementsByClassName('info')[0].innerText)
      setData(oldData)
      addCaseModal({
        onSubmit (data) {
          editor.selection.moveToBookmark(bookmark)
          const url = data.data.cover && data.data.cover !== '' ? data.data.cover : `https://static.bingotalk.cn/bingodev/image/2025080711234828.png`
          const tagUrl = `https://static.bingotalk.cn/bingodev/image/2025080711244405.png`
          const media = `<div data-id="${data.id}" class='case_card mceNonEditable' style='width: 100%;display: flex;justify-content: center'>
                           <div class="info" style="display:none">${JSON.stringify(data)}</div>
                           <div class="case_main" style="width: 60%;height: 100%;background-color: rgba(245, 250, 255, 1);border-radius: 10px;padding-bottom: 10px;margin-bottom:2px;position: relative;border: 1px solid rgba(181, 218, 255, 1);box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.06);">
                              <div style='position: absolute;border-radius:10px;top: 10px;right: 10px;width: 100px;height: 25px;background: linear-gradient(94.27deg, #209CFF 3.47%, #68E0CF 190.23%);display: flex;align-items: center;justify-content: center;padding: 5px;color: white;font-size: 14px'>
                                <img class="default-img" src="${tagUrl}" alt='' style="width: 16px;height: 16px;object-fit: contain;margin-right: 8px"/>
                                交互案例
                              </div>
                              <div style="width: 100%;height: calc(100% - 50px - 30px);">
                                <img class="default-img" src="${url}" alt='' style="width: 100%;height: 250px;object-fit: cover;border-radius: 10px 10px 0 0;"/>
                              </div>
                              <div style="width: 100%;height: 60px;padding: 5px 10px">
                                <div style="width: 100%;height: 45%;display: flex;align-items: center;font-size: 16px;font-weight: 500;">
                                  ${data.title || ''}
                                </div>
                                <div style="width: 100%;height: 55%;display: flex;align-items: center;font-size: 12px;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;overflow: hidden;white-space: pre-wrap;text-overflow: ellipsis;color: rgba(51, 51, 51, 1);">
                                  ${data.subtitle || ''}
                                </div>
                              </div>
                            <div class="case-btn" style="margin-left: 10px;height: 20px;width: 35%;background-color: rgba(47, 128, 237, 1);color: white;font-size: 16px;border-radius: 15px;display: flex;justify-content: center;align-items: center;cursor: pointer;">立即体验</div>
                          </div>
                         </div>`
          editor.selection.setContent(media)
          closeCase()
        }
      })
    }
  })
  editor.ui.registry.addButton('casecardremovecontrol', {
    icon: 'remove',
    tooltip: '删除案例',
    onAction: (editor) => {
      const bookmark = tinymce.activeEditor.selection.getBookmark()
      MessageBox.confirm('确认删除案例？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        tinymce.activeEditor.selection.moveToBookmark(bookmark)
        tinymce.activeEditor.focus()
        tinymce.activeEditor.selection.setContent('')
        tinymce.activeEditor.selection.collapse()
      })
    }
  })
  return editor.ui.registry.addButton('addCase', {
    icon: 'addCaseIcon',
    test: '添加案例',
    tooltip: '添加案例',
    onAction: () => {
      // const selectContent = editor.selection.getNode()
      // const bookmark = editor.selection.getBookmark()
      addCaseModal({
        onSubmit (data) {
          const url = data.data.cover && data.data.cover !== '' ? data.data.cover : `https://static.bingotalk.cn/bingodev/image/2025080711234828.png`
          const tagUrl = `https://static.bingotalk.cn/bingodev/image/2025080711244405.png`
          const media = `<div data-id="${data.id}" class='case_card mceNonEditable' style='width: 100%;display: flex;justify-content: center'>
                           <div class="info" style="display:none">${JSON.stringify(data)}</div>
                           <div class="case_main" style="width: 60%;height: 100%;background-color: rgba(245, 250, 255, 1);border-radius: 10px;padding-bottom: 10px;margin-bottom:2px;position: relative;border: 1px solid rgba(181, 218, 255, 1);box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.06);">
                              <div style='position: absolute;border-radius:10px;top: 10px;right: 10px;width: 100px;height: 25px;background: linear-gradient(94.27deg, #209CFF 3.47%, #68E0CF 190.23%);display: flex;align-items: center;justify-content: center;padding: 5px;color: white;font-size: 14px'>
                                <img class="default-img" src="${tagUrl}" alt='' style="width: 16px;height: 16px;object-fit: contain;margin-right: 8px"/>
                                交互案例
                              </div>
                              <div style="width: 100%;height: calc(100% - 50px - 30px);">
                                <img class="default-img" src="${url}" alt='' style="width: 100%;height: 250px;object-fit: cover;border-radius: 10px 10px 0 0;"/>
                              </div>
                              <div style="width: 100%;height: 60px;padding: 5px 10px">
                                <div style="width: 100%;height: 45%;display: flex;align-items: center;font-size: 16px;font-weight: 500;">
                                  ${data.title || ''}
                                </div>
                                <div style="width: 100%;height: 55%;display: flex;align-items: center;font-size: 12px;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;overflow: hidden;white-space: pre-wrap;text-overflow: ellipsis;color: rgba(51, 51, 51, 1);">
                                  ${data.subtitle || ''}
                                </div>
                              </div>
                            <div class="case-btn" style="margin-left: 10px;height: 20px;width: 35%;background-color: rgba(47, 128, 237, 1);color: white;font-size: 16px;border-radius: 15px;display: flex;justify-content: center;align-items: center;cursor: pointer;">立即体验</div>
                          </div>
                         </div>`
          editor.selection.setContent(media)
          const content = editor.getContent()
          editor.setContent(content)
          closeCase()
        }
      })
    }
  })
}
