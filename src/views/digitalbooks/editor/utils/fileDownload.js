import tinymce from 'tinymce/tinymce'
import { getFileUploadAuthor } from '@/api/user-api'
import axios from 'axios'
import { Notification, MessageBox } from 'element-ui'
export function fileDownLoad (editor, url) {
  return editor.ui.registry.addButton('fileDownLoad', {
    icon: 'fileDownload',
    tooltip: '插入附件',
    onAction: function () {
      MessageBox.confirm('该文件是否支持下载?', '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning',
        center: true
      }).then(() => {
        var input = document.createElement('input')
        input.type = 'file'
        // 执行上传文件操作
        input.addEventListener('change', async function (e) {
          var file = e.target.files[0]
          let mediaType = ''
          if (file.type.indexOf('video/') > -1) {
            mediaType = 'VIDEO'
          } else if (file.type.indexOf('image/') > -1) {
            mediaType = 'IMAGE'
          } else {
            mediaType = 'FILE'
          }
          const { data } = await getFileUploadAuthor({
            mediaType: mediaType,
            contentType: '',
            quantity: 1,
            fileName: file.name
          })
          const ossCDN = data[0].ossConfig.ossCDN
          try {
            const formData = new FormData()
            formData.append('success_action_status', '200')
            formData.append('callback', '')
            formData.append('key', data[0].fileName)
            formData.append('policy', data[0].policy)
            formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
            formData.append('signature', data[0].signature)
            formData.append('file', file)
            const notif = Notification({
              title: `${file.name}上传中`,
              message: '0%',
              duration: 0
            })
            await axios.post(data[0].ossConfig.host, formData, {
              onUploadProgress: (progress) => {
                const complete = Math.floor(progress.loaded / progress.total * 100)
                notif.message = complete + '%'
                console.log(complete)
                if (complete >= 100) {
                  notif.close()
                }
              }
            })
            const type = data[0].fileName.substring(data[0].fileName.lastIndexOf('.') + 1)
            const fileType = getFileType(type)
            let url = ''
            if (fileType === 'IMAGE') {
              url = `${ossCDN}/${data[0].fileName}`
            }
            if (fileType === 'VIDEO') {
              url = `${ossCDN}/${data[0].fileName}?x-oss-process=video/snapshot,f_png,w_300,t_0`
            } else {
              url = getsvg(fileType)
            }
            const size = formatBytes(file.size)
            const media = `<div class='file_download mceNonEditable' style='width:100%;min-height:78px;padding-left: 5%;padding-right: 5%;margin-bottom:2px;box-sizing: border-box;background: #E3F5FF;border-radius: 5px;box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);display:flex;justify-content:space-between'>
            <p style="font-weight:500;font-size:18px;line-height:200%;height:100%;white-space: nowrap;margin-right: 10px;">附件:</p>
            <img style='width:50px;height:50px;margin-top: 20px;' src='${url}' />
            <div style='width:50%; overflow:hidden'>
            <p style='overflow:hidden;text-overflow:ellipsis;white-space:nowrap;'>${file.name}</p>
            <p>${size === 'NaNundefined' ? '0kb' : size}</p>
            </div>
            <div>
            <img class='show_button' style='cursor: pointer; width:50px;margin-top: 20px;' src='https://static.bingotalk.cn/bingodev/image/2024092515214073.png'/><p style='display:none'>${ossCDN}/${data[0].fileName}</p><p style='display:none'>${file.name}</p>
            </div>
            <div>
            <img class='download_button' style='cursor: pointer; width:50px;margin-top: 20px;' src='https://static.bingotalk.cn/bingodev/image/2024092515220781.png'/><p style='display:none'>${ossCDN}/${data[0].fileName}</p><p style='display:none'>${file.name}</p>
            </div>
            </div>`
            tinymce.activeEditor.selection.setContent(media)
            const content = editor.getContent()
            editor.setContent(content)
          } catch (error) {
            console.log(error)
            // failure('文件上传失败，请重试')
          }
        }, false)
        // 触发点击事件，打开选择文件的对话框
        input.click()
      }).catch(() => {
        var input = document.createElement('input')
        input.type = 'file'
        // 执行上传文件操作
        input.addEventListener('change', async function (e) {
          var file = e.target.files[0]
          let mediaType = ''
          if (file.type.indexOf('video/') > -1) {
            mediaType = 'VIDEO'
          } else if (file.type.indexOf('image/') > -1) {
            mediaType = 'IMAGE'
          } else {
            mediaType = 'FILE'
          }
          const { data } = await getFileUploadAuthor({
            mediaType: mediaType,
            contentType: '',
            quantity: 1,
            fileName: file.name
          })
          const ossCDN = data[0].ossConfig.ossCDN
          try {
            const formData = new FormData()
            formData.append('success_action_status', '200')
            formData.append('callback', '')
            formData.append('key', data[0].fileName)
            formData.append('policy', data[0].policy)
            formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
            formData.append('signature', data[0].signature)
            formData.append('file', file)
            const notif = Notification({
              title: `${file.name}上传中`,
              message: '0%',
              duration: 0
            })
            await axios.post(data[0].ossConfig.host, formData, {
              onUploadProgress: (progress) => {
                const complete = Math.floor(progress.loaded / progress.total * 100)
                notif.message = complete + '%'
                console.log(complete)
                if (complete >= 100) {
                  notif.close()
                }
              }
            })
            const type = data[0].fileName.substring(data[0].fileName.lastIndexOf('.') + 1)
            const fileType = getFileType(type)
            let url = ''
            if (fileType === 'IMAGE') {
              url = `${ossCDN}/${data[0].fileName}`
            }
            if (fileType === 'VIDEO') {
              url = `${ossCDN}/${data[0].fileName}?x-oss-process=video/snapshot,f_png,w_300,t_0`
            } else {
              url = getsvg(fileType)
            }
            const size = formatBytes(file.size)
            var media = `<div class='file_download mceNonEditable' style='width:100%;min-height:78px;padding-left: 5%;padding-right: 5%;margin-bottom:2px;box-sizing: border-box;background: #E3F5FF;border-radius: 5px;box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);display:flex;justify-content:space-between'>
            <p style="font-weight:500;font-size:18px;line-height:200%;height:100%;white-space: nowrap;margin-right: 10px;">附件:</p>
            <img style='width:50px;height:50px;margin-top: 20px;' src='${url}'></img>
            <div style='width:50%; overflow:hidden'>
            <p style='overflow:hidden;text-overflow:ellipsis;white-space:nowrap;'>${file.name}</p>
            <p>${size === 'NaNundefined' ? '0kb' : size}</p>
            </div>
            <div>
            <img class='show_button' style='cursor: pointer; width:50px;margin-top: 20px;' src='https://static.bingotalk.cn/bingodev/image/2024092515214073.png'/><p style='display:none'>${ossCDN}/${data[0].fileName}</p><p style='display:none'>${file.name}</p>
            </div>
            </div>`
            tinymce.activeEditor.selection.setContent(media)
            const content = editor.getContent()
            editor.setContent(content)
          } catch (error) {
            console.log(error)
            // failure('文件上传失败，请重试')
          }
        }, false)
        // 触发点击事件，打开选择文件的对话框
        input.click()
      })
    }
  })
}
function getFileType (str) {
  var result = ''
  var imglist = ['png', 'jpg', 'jpeg', 'bmp', 'gif', 'webp']
  // 进行图片匹配
  result = imglist.some(function (item) {
    return item === str
  })
  if (result) {
    result = 'IMAGE'
    return result
  }
  // 匹配txt
  var txtlist = ['txt']
  result = txtlist.some(function (item) {
    return item === str
  })
  if (result) {
    result = 'TXT'
    return result
  }
  // 匹配 excel
  var excelist = ['xls', 'xlsx']
  result = excelist.some(function (item) {
    return item === str
  })
  if (result) {
    result = 'EXCEL'
    return result
  }
  // 匹配 word
  var wordlist = ['doc', 'docx']
  result = wordlist.some(function (item) {
    return item === str
  })
  if (result) {
    result = 'WORD'
    return result
  }
  // 匹配 pdf
  var pdflist = ['pdf']
  result = pdflist.some(function (item) {
    return item === str
  })
  if (result) {
    result = 'PDF'
    return result
  }
  // 匹配 ppt
  var pptlist = ['ppt', 'pptx']
  result = pptlist.some(function (item) {
    return item === str
  })
  if (result) {
    result = 'PPT'
    return result
  }
  // 匹配 视频
  var videolist = ['mp4', 'm2v', 'mkv']
  result = videolist.some(function (item) {
    return item === str
  })
  if (result) {
    result = 'VIDEO'
    return result
  }
  // 匹配 音频
  var radiolist = ['mp3', 'wav', 'wmv']
  result = radiolist.some(function (item) {
    return item === str
  })
  if (result) {
    result = 'RADIO'
    return result
  }
  // 匹配压缩包
  var zipList = ['zip', 'rar']
  result = zipList.some(function (item) {
    return item === str
  })
  if (result) {
    result = 'ZIP'
    return result
  }
  // 其他 文件类型
  result = 'OTHER'
  return result
}
function getsvg (type) {
  if (type === 'WORD') {
    return 'https://static.bingotalk.cn/bingodev/image/2024022915045491.png'
  }
  if (type === 'EXCEL') {
    return 'https://static.bingotalk.cn/bingodev/image/2024022915055149.png'
  }
  if (type === 'PDF') {
    return 'https://static.bingotalk.cn/bingodev/image/2024022915061825.png'
  }
  if (type === 'PPT') {
    return 'https://static.bingotalk.cn/bingodev/image/2024022915064182.png'
  }
  if (type === 'ZIP') {
    return 'https://static.bingotalk.cn/bingodev/image/2024022915042612.png'
  }
  return 'https://static.bingotalk.cn/bingodev/image/2024022915073132.png'
}

function formatBytes (value) {
  if (value == null || value === '') {
    return '0 Bytes'
  }
  // eslint-disable-next-line no-array-constructor
  var unitArr = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  var index = 0
  var srcsize = parseFloat(value)
  index = Math.floor(Math.log(srcsize) / Math.log(1024))
  var size = srcsize / Math.pow(1024, index)
  size = size.toFixed(2)// 保留的小数位数
  return size + unitArr[index]
}
