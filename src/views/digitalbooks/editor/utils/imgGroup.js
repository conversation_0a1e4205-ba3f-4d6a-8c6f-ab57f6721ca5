import tinymce from 'tinymce/tinymce'
import { addImgGroup, closeImgGroup, setData } from '../components/imgGroup'
import { MessageBox } from 'element-ui'
export function imgGroup (editor, url) {
  // 添加悬浮 上下文工具栏
  editor.ui.registry.addContextToolbar('imglisteditcontrol', {
    // 浮层的触发条件
    predicate: function (node) {
      return node.classList.contains('img_list')
      // return node.nodeName.toLowerCase() === 'p'
    },
    items: 'changecontrol removecontrol', // 显示的工具列表
    position: 'selection' // 工具栏放置位置  selection node line
    // scope: 'editor'
  })
  // 浮层注册编辑和删除按钮
  editor.ui.registry.addButton('changecontrol', {
    icon: 'edit-block',
    tooltip: '编辑图片集',
    onAction: () => {
      const bookmark = tinymce.activeEditor.selection.getBookmark()
      const selectContent = tinymce.activeEditor.selection.getNode()
      const oldData = JSON.parse(selectContent.getElementsByClassName('info')[0].innerText)
      setData({
        title: oldData.title,
        content: oldData.content
      })
      addImgGroup({
        onSubmit (data) {
          tinymce.activeEditor.selection.moveToBookmark(bookmark)
          const media = `
  <div class='img_list mceNonEditable'
       style='margin-bottom:2px;width:100%;background:#E3F5FF;border-radius: 5px;box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);overflow:hidden;padding:20px;box-sizing:border-box;display:flex;align-items:center;justify-content:space-between;'>
    <div style='float:left;width:40%;box-sizing:border-box;padding-right:10px;'>
      <p style='font-weight:700;font-size:16px;'>图片</p>
      <p style='color:#333333;font-size:14px;width:100%;margin-top:10px;'>${data.title}</p>
      <p style='margin-top:10px;font-size:14px;'>${data.content.length}张</p>
    </div>
    <div style='width:50%;display:flex;justify-content:center;align-items:center;'>
      <img class='img_card_button'
           src="${data.content[0]?.src || ''}"
           style='max-width:100%;max-height:100px;display:inline-block;vertical-align:middle;object-fit: cover'>
    <p class='info' style='display:none;'>${JSON.stringify(data)}</p>
    </div>
  </div>
`
          tinymce.activeEditor.selection.setContent(media)
          closeImgGroup()
        }
      })
    }
  })
  editor.ui.registry.addButton('removecontrol', {
    icon: 'remove',
    tooltip: '删除图片集',
    onAction: (editor) => {
      const bookmark = tinymce.activeEditor.selection.getBookmark()
      MessageBox.confirm('确认删除图片集？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        tinymce.activeEditor.selection.moveToBookmark(bookmark)
        tinymce.activeEditor.focus()
        tinymce.activeEditor.selection.setContent('')
        tinymce.activeEditor.selection.collapse()
      })
    }
  })

  return editor.ui.registry.addButton('imgGroup', {
    icon: 'imgGroup',
    tooltip: '添加图片集',
    onAction: function () {
      let type = 'add'
      const selectContent = tinymce.activeEditor.selection.getNode()
      const bookmark = tinymce.activeEditor.selection.getBookmark()
      if (selectContent.classList.contains('img_list')) {
        const oldData = JSON.parse(selectContent.getElementsByClassName('info')[0].innerText)
        setData({
          title: oldData.title,
          content: oldData.content
        })
        type = 'edit'
      }
      addImgGroup({
        onSubmit (data) {
          if (type === 'add') {
            const media = `
  <div class='img_list mceNonEditable'
       style='margin-bottom:2px;width:100%;background:#E3F5FF;border-radius: 5px;box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);overflow:hidden;padding:20px;box-sizing:border-box;display:flex;align-items:center;justify-content:space-between;'>
    <div style='float:left;width:40%;box-sizing:border-box;padding-right:10px;'>
      <p style='font-weight:700;font-size:16px;'>图片</p>
      <p style='color:#333333;font-size:14px;width:100%;margin-top:10px;'>${data.title}</p>
      <p style='margin-top:10px;font-size:14px;'>${data.content.length}张</p>
    </div>
    <div style='width:50%;display:flex;justify-content:center;align-items:center;'>
      <img class='img_card_button'
           src="${data.content[0]?.src || ''}"
           style='max-width:100%;max-height:100px;display:inline-block;vertical-align:middle;object-fit: cover'>
    <p class='info' style='display:none;'>${JSON.stringify(data)}</p>
    </div>
  </div>
`
            tinymce.activeEditor.selection.setContent(media)
            const content = editor.getContent()
            editor.setContent(content)
          }
          if (type === 'edit') {
            tinymce.activeEditor.selection.moveToBookmark(bookmark)
            const media = `
  <div class='img_list mceNonEditable'
       style='margin-bottom:2px;width:100%;background:#E3F5FF;border-radius: 5px;box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);overflow:hidden;padding:20px;box-sizing:border-box;display:flex;align-items:center;justify-content:space-between;'>
    <div style='float:left;width:40%;box-sizing:border-box;padding-right:10px;'>
      <p style='font-weight:700;font-size:16px;'>图片</p>
      <p style='color:#333333;font-size:14px;width:100%;margin-top:10px;'>${data.title}</p>
      <p style='margin-top:10px;font-size:14px;'>${data.content.length}张</p>
    </div>
    <div style='width:50%;display:flex;justify-content:center;align-items:center;'>
      <img class='img_card_button'
           src="${data.content[0]?.src || ''}"
           style='max-width:100%;max-height:100px;display:inline-block;vertical-align:middle;object-fit: cover'>
    <p class='info' style='display:none;'>${JSON.stringify(data)}</p>
    </div>
  </div>
`
            tinymce.activeEditor.selection.setContent(media)
          }
          closeImgGroup()
        }

      })
    }
  })
}
