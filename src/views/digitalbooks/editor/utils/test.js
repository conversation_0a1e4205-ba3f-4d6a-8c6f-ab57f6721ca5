import tinymce from 'tinymce/tinymce'
import { MessageBox } from 'element-ui'
// import { addTestPopModal, setData } from '../components/addTestPop'
import { addExerciseModal, setData, closeExercise } from '../components/addExercise/addExercise'
import { createTest } from '@/api/test-api'
import store from '../../../../store'
import router from '../../../../router'
export function test (editor, url) {
  editor.ui.registry.addContextToolbar('testcardeditcontrol', {
    // 浮层的触发条件
    predicate: function (node) {
      return node.classList.contains('test_card')
    },
    items: 'testcardchangecontrol testcardremovecontrol', // 显示的工具列表
    position: 'selection' // 工具栏放置位置  selection node line
    // scope: 'editor'
  })
  // 浮层注册编辑和删除按钮
  editor.ui.registry.addButton('testcardchangecontrol', {
    icon: 'edit-block',
    tooltip: '编辑题集',
    onAction: () => {
      const bookmark = tinymce.activeEditor.selection.getBookmark()
      tinymce.activeEditor.selection.moveToBookmark(bookmark)
      const selectContent = tinymce.activeEditor.selection.getNode()
      setData({
        testId: selectContent.getAttribute('data-id'),
        ids: selectContent.getAttribute('data-ids')
      })
      editor.getBody().focus()
      addExerciseModal({
        updateTest(testTitle, testId, length, ids, flag = false) {
          const url = 'https://static.bingotalk.cn/bingodev/image/2025081916022964.png'
          var newContent = tinymce.activeEditor.dom.select(`div[data-id="${testId}"]`)[0]
          tinymce.activeEditor.selection.select(newContent)
          tinymce.activeEditor.selection.moveToBookmark(bookmark)
          const media = `<div class='test_card mceNonEditable' data-id="${testId}" data-ids="${ids}"  style='margin-bottom:2px;width:100%;height:160px;background: #E3F5FF;border-radius: 5px;box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);overflow:hidden;padding:15px;box-sizing: border-box;position:relative;display: flex'>
                             <img src="${url}" alt='' style="height: 100%; object-fit: contain" />
                             <div style="height: 100%; padding-left: 20px;display: flex;flex-direction: column;justify-content: center;gap: 10px">
                                <div style="color:#31373B;font-size:18px;font-weight:700;height: 40px;line-height: 40px;flex-shrink: 1">
                                  互动学习
                                </div>
                                <div style="font-size: 14px;color: #4F4F4F;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;overflow: hidden;white-space: pre-wrap;text-overflow: ellipsis;">
                                  ${testTitle}（${length}道题）
                                </div>
                             </div>
                             <div class='do_test' style="position: absolute; right: 5%; top: 29%; padding: 5px 20px; border: 1px solid #2D9CDB; border-radius: 5px; text-align: center;  color: #2d9cdb;cursor: pointer;">
                                去做题
                             </div>
                           </div>`
          tinymce.activeEditor.selection.setContent(media)
          editor.getBody().focus()
          if (flag) {
            setData({ testId: testId, ids: ids })
          }
        }
      })
    }
  })
  editor.ui.registry.addButton('testcardremovecontrol', {
    icon: 'remove',
    tooltip: '删除答题',
    onAction: (editor) => {
      const bookmark = tinymce.activeEditor.selection.getBookmark()
      MessageBox.confirm('确认删除习题集？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        tinymce.activeEditor.selection.moveToBookmark(bookmark)
        tinymce.activeEditor.focus()
        tinymce.activeEditor.selection.setContent('')
        tinymce.activeEditor.selection.collapse()
      })
    }
  })
  return editor.ui.registry.addButton('test', {
    icon: 'paper',
    tooltip: '添加题集',
    onAction: function () {
      const bookmark = tinymce.activeEditor.selection.getBookmark()
      const selectContent = tinymce.activeEditor.selection.getNode()
      console.log(selectContent.getAttribute('data-ids'))
      if (selectContent.getAttribute('data-ids') !== null) {
        setData({
          testId: selectContent.getAttribute('data-id'),
          ids: selectContent.getAttribute('data-ids')
        })
      }
      addExerciseModal({
        updateTest(testTitle, testId, length, ids, flag = false) {
          const url = 'https://static.bingotalk.cn/bingodev/image/2025081916022964.png'
          if (selectContent.getAttribute('data-ids') !== null) {
            var newContent = tinymce.activeEditor.dom.select(`div[data-id="${testId}"]`)[0]
            tinymce.activeEditor.selection.select(newContent)
            tinymce.activeEditor.selection.moveToBookmark(bookmark)
            const media = `<div class='test_card mceNonEditable' data-id="${testId}" data-ids="${ids}"  style='margin-bottom:2px;width:100%;height:160px;background: #E3F5FF;border-radius: 5px;box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);overflow:hidden;padding:15px;box-sizing: border-box;position:relative;display: flex'>
                             <img src="${url}" alt='' style="height: 100%; object-fit: contain" />
                             <div style="height: 100%; padding-left: 20px;display: flex;flex-direction: column;justify-content: center;gap: 10px">
                                <div style="color:#31373B;font-size:18px;font-weight:700;height: 40px;line-height: 40px;flex-shrink: 1">
                                  互动学习
                                </div>
                                <div style="font-size: 14px;color: #4F4F4F;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;overflow: hidden;white-space: pre-wrap;text-overflow: ellipsis;">
                                  ${testTitle}（${length}道题）
                                </div>
                             </div>
                             <div class='do_test' style="position: absolute; right: 5%; top: 29%; padding: 5px 20px; border: 1px solid #2D9CDB; border-radius: 5px; text-align: center;  color: #2d9cdb;cursor: pointer;">
                                去做题
                             </div>
                           </div>`
            tinymce.activeEditor.selection.setContent(media)
          } else {
            tinymce.activeEditor.selection.moveToBookmark(bookmark)
            const media = `<div class='test_card mceNonEditable' data-id="${testId}" data-ids="${ids}"  style='margin-bottom:2px;width:100%;height:160px;background: #E3F5FF;border-radius: 5px;box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);overflow:hidden;padding:15px;box-sizing: border-box;position:relative;display: flex'>
                             <img src="${url}" alt='' style="height: 100%; object-fit: contain" />
                             <div style="height: 100%; padding-left: 20px;display: flex;flex-direction: column;justify-content: center;gap: 10px">
                                <div style="color:#31373B;font-size:18px;font-weight:700;height: 40px;line-height: 40px;flex-shrink: 1">
                                  互动学习
                                </div>
                                <div style="font-size: 14px;color: #4F4F4F;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;overflow: hidden;white-space: pre-wrap;text-overflow: ellipsis;">
                                  ${testTitle}（${length}道题）
                                </div>
                             </div>
                             <div class='do_test' style="position: absolute; right: 5%; top: 29%; padding: 5px 20px; border: 1px solid #2D9CDB; border-radius: 5px; text-align: center;  color: #2d9cdb;cursor: pointer;">
                                去做题
                             </div>
                           </div>`
            tinymce.activeEditor.selection.setContent(media)
            const content = editor.getContent()
            editor.setContent(content)
          }
          if (flag) {
            setData({ testId: testId, ids: ids })
          }
          closeExercise()
        }
      })
    }
  })
}
