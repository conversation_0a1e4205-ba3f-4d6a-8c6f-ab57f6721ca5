<template>
  <div class="ai-body" v-if="show">
    <div class="ai-main" v-loading="loading" element-loading-background="rgba(255, 255, 255, 0.5)" element-loading-text="AI生成中">
      <el-form ref='form' :model='form' label-position='top'>
        <el-form-item label="出题要求">
          <el-input v-model='form.aiQuestion' placeholder='例如：难度中等，核心知识点，注重思维与步骤的规范性。' type='textarea' :rows='4' />
        </el-form-item>
      </el-form>
      <div class="bottom">
        <div class="bottom-btn" @click="close">
          取消
        </div>
        <div class="bottom-btn bottom-ai-btn" @click="generateQuestion">
          <img src='@/assets/digitalbooks/edit/ai-btn.png' />
          立即生成
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { generateQuestionByAigc } from '@/api/test-api'
import store from '@/store'
import router from '@/router'
export default {
  name: 'AddQuestion',
  data() {
    return {
      show: false,
      type: '',
      form: {
        aiQuestion: ''
      },
      loading: false
    }
  },
  methods: {
    open(type) {
      this.type = type
      this.show = true
    },
    close() {
      this.form.aiQuestion = ''
      this.type = ''
      this.show = false
    },
    async generateQuestion() {
      try {
        this.loading = true
        const { data } = await generateQuestionByAigc({
          questionType: this.type,
          digitalCatalogueId: store.state.app.activeCatalogueId,
          aiQuestion: this.form.aiQuestion
        }, {
          authorization: 'Bearer ' + router.currentRoute.query.token
        })
        if (!data) {
          throw new Error('AI算力加速中，请重试')
        }
        this.$emit('addQuestion', data)
        this.close()
      } catch (e) {
        this.$message.error('AI算力加速中，请重试')
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped lang="scss">
.ai-body{
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 102;
  display: flex;
  align-items: center;
  justify-content: center;
  .ai-main{
    width: 65%;
    background: white;
    border-radius: 10px;
    padding: 10px 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    .bottom{
      width: 100%;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 20px;
      margin-top: 5px;
      .bottom-ai-btn{
        background: linear-gradient(95.99deg, #B721FF -67.63%, #21D4FD 109.16%);
        color: rgba(255, 255, 255, 1) !important;
        border: none;
        cursor: pointer;
        width: 90px !important;
        img{
          width: 20px;
          height: 20px;
          margin-right: 5px;
        }
        &:hover{
          background-color: rgba(30, 100, 200, 1);
        }
      }
      .bottom-btn{
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80px;
        height:30px;
        font-size: 12px;
        color: rgba(102, 102, 102, 1);
        border-radius: 5px;
        cursor: pointer;
        border: 1px solid rgba(224, 224, 224, 1);
        margin-left: 10px;
        &:hover{
          background-color: rgba(200, 220, 255, 1);
        }
      }
    }
  }
}
</style>
