
export const EXERCISE_PROTOCOL = {
  VERSION: '1.0',

  DOCUMENT_HEADER: '=== 习题集导出文档 ===',
  DOCUMENT_FOOTER: '=== 文档结束 ===',

  HTML_METADATA_START: '<!-- BINGO_EXERCISE_V1.0_START -->',
  HTML_METADATA_END: '<!-- BINGO_EXERCISE_V1.0_END -->',

  QUESTION_TYPES: {
    CHOICE: '选择题',
    FILL_IN_THE_BLANK_INPUT: '填空题',
    SIMPLE_CHOOSE: '判断题',
    ESSAY_QUESTION: '简答题'
  },

  QUESTION_TYPE_REVERSE: {
    '选择题': 'CHOICE',
    '填空题': 'FILL_IN_THE_BLANK_INPUT',
    '判断题': 'SIMPLE_CHOOSE',
    '简答题': 'ESSAY_QUESTION'
  },

  TEXT_MARKERS: {
    QUESTION_START: '[{type}-{index}] ({score}分)',
    QUESTION_END: '---',
    CORRECT_ANSWER: '✓',
    WRONG_ANSWER: '✗',
    FIELD_SEPARATOR: ': ',
    OPTION_SEPARATOR: ' | ',
    KNOWLEDGE_SEPARATOR: ','
  },

  REQUIRED_FIELDS: {
    EXERCISE: ['exerciseName', 'testId'],
    QUESTION: ['question', 'questionType', 'answer']
  },

  VALIDATION_RULES: {
    exerciseName: {
      required: true,
      maxLength: 100,
      message: '习题集名称不能为空且不超过100字符'
    },
    testId: {
      required: true,
      pattern: /^\d+$/,
      message: '习题集ID必须为数字'
    },
    question: {
      required: true,
      maxLength: 1000,
      message: '题目内容不能为空且不超过1000字符'
    },
    questionType: {
      required: true,
      enum: ['CHOICE', 'FILL_IN_THE_BLANK_INPUT', 'SIMPLE_CHOOSE', 'ESSAY_QUESTION'],
      message: '题目类型必须为指定的类型之一'
    },
    score: {
      type: 'number',
      min: 0,
      max: 100,
      message: '分值必须为0-100之间的数字'
    }
  }
}

export function formatQuestionData(questionData) {
  const standardData = {
    id: questionData.id || '',
    question: questionData.question || '',
    questionType: questionData.questionType || '',
    answer: questionData.answer || '',
    analysis: questionData.analysis || '',
    score: Number(questionData.score) || 0,
    knowledges: Array.isArray(questionData.knowledges) ? questionData.knowledges : [],
    answerOptionList: Array.isArray(questionData.answerOptionList) ? questionData.answerOptionList : []
  }

  return standardData
}

export function formatExerciseData(exerciseData) {
  const standardData = {
    version: EXERCISE_PROTOCOL.VERSION,
    exportTime: new Date().toISOString(),
    exerciseName: exerciseData.exerciseName || '习题集',
    testId: exerciseData.testId || '0',
    catalogueId: exerciseData.catalogueId || '',
    questionList: []
  }

  const allQuestions = [
    ...(exerciseData.choiceList || []),
    ...(exerciseData.fillList || []),
    ...(exerciseData.simpleList || []),
    ...(exerciseData.essayList || [])
  ]

  standardData.questionList = allQuestions.map(formatQuestionData)

  return standardData
}

export function generateQuestionMarker(type, index, score) {
  const typeName = EXERCISE_PROTOCOL.QUESTION_TYPES[type] || type
  return `[${typeName}-${index}] (${score}分)`
}

export function parseQuestionMarker(marker) {
  const regex = /\[(.+)-(\d+)\] \((\d+)分\)/
  const match = marker.match(regex)

  if (!match) {
    throw new Error('无效的题目标记格式')
  }

  const [, typeName, index, score] = match
  const type = EXERCISE_PROTOCOL.QUESTION_TYPE_REVERSE[typeName]

  if (!type) {
    throw new Error(`不支持的题目类型: ${typeName}`)
  }

  return {
    type,
    index: parseInt(index),
    score: parseInt(score)
  }
}

export function formatChoiceOptions(options) {
  const labels = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H']
  return options.map((option, index) => {
    const label = labels[index] || (index + 1)
    const marker = option.right ? ' ✓' : ''
    return `${label}. ${option.answer}${marker}`
  }).join('\n')
}

export function parseChoiceOptions(optionsText) {
  const lines = optionsText.split('\n').filter(line => line.trim())
  const options = []

  lines.forEach(line => {
    const match = line.match(/^([A-H]|\d+)\.\s*(.+?)(\s*✓)?$/)
    if (match) {
      const [, , answer, isCorrect] = match
      options.push({
        answer: answer.trim(),
        right: !!isCorrect
      })
    }
  })

  return options
}

export function formatSimpleChooseOptions(options) {
  return options.map(option => {
    const marker = option.right ? ' ✓' : ' ✗'
    return `${option.answer}${marker}`
  }).join(' | ')
}

export function parseSimpleChooseOptions(optionsText) {
  const parts = optionsText.split(' | ')
  return parts.map(part => {
    const isCorrect = part.includes('✓')
    const answer = part.replace(/\s*[✓✗]\s*$/, '').trim()
    return {
      answer,
      right: isCorrect
    }
  })
}

export default EXERCISE_PROTOCOL
