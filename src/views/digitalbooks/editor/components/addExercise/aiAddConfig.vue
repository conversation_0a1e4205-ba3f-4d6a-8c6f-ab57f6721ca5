<template>
  <div class="ai-body no-conversion" v-if="show">
    <div class="ai-main" v-loading='fileLoading' element-loading-background="rgba(255, 255, 255, 0.5)" element-loading-text="文件上传中">
      <div class="title">
        AI生成习题集
      </div>
      <div class="content">
        <el-form ref='formBasic' :model='formData' label-position='top'>
          <div class="basic">
            <div class="content-title">基础信息</div>
            <el-form-item label='习题集名称'>
              <el-input v-model='formData.title' placeholder='请输入习题集名称' />
            </el-form-item>
            <el-form-item label='出题要求'>
              <el-input v-model='formData.aigcPrompt' placeholder='请输入出题要求' type="textarea" :rows='3' maxlength="200" show-word-limit/>
            </el-form-item>
            <el-form-item>
              <template slot='label'>
                <span>参考资料</span>
                <span class='sub-label'>知识点提纲、教材摘要、课堂笔记等（可选，会用于更准确的生题）</span>
              </template>
              <el-upload
                class="uploader"
                action=""
                drag
                mutiple
                accept='.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.zip,.txt'
                :show-file-list="false"
                :before-upload="beforeUpload">
                <div class='el-upload__title' :class="{'mt': fileList.length !== 0}">点击或拖拽上传文件(支持上传10个文件)</div>
                <div class='el-upload__tip' v-if="fileList.length === 0">支持Word、PDF、PPT、EXCEL、TXT等格式，每个文件不超过 150MB</div>
                <div class="file-list">
                  <div class="file-item" v-for="(file, index) in fileList" :key="index">
                    <img :src="officeImageUrl(file.name)" alt="file icon" />
                    <div class="file-name">{{ file.name }}</div>
                    <i class="el-icon-error delete" @click.stop="deleteFile(file)"></i>
                  </div>
                </div>
              </el-upload>
            </el-form-item>
          </div>
        </el-form>
        <el-form ref='formConfig' :model='formData'>
          <div class="config">
            <div class="content-title">题型与数量配置</div>
            <div class="content-subtitle">可根据需要调整各题型数量，系统将按比例与覆盖度生成题目。</div>
            <el-row :gutter='10'>
              <el-col :span='6'>
                <el-form-item label='选择题'>
                  <el-input-number size="small" v-model="formData.choiceNum" style='width: 60%' :min='0'/>
                </el-form-item>
              </el-col>
              <el-col :span='6'>
                <el-form-item label='填空题'>
                  <el-input-number size="small" v-model="formData.fillInTheBlankInputNum" style='width: 60%' :min='0'/>
                </el-form-item>
              </el-col>
              <el-col :span='6'>
                <el-form-item label='判断题'>
                  <el-input-number size="small" v-model="formData.simpleChooseNum" style='width: 60%' :min='0'/>
                </el-form-item>
              </el-col>
              <el-col :span='6'>
                <el-form-item label='简答题'>
                  <el-input-number size="small" v-model="formData.essayQuestionNum" style='width: 60%' :min='0'/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter='10' class="mt10">
              <el-col :span='6'>
                <el-form-item label='难度'>
                  <el-select v-model='formData.level' style='width: 70%'>
                    <el-option
                      v-for='item in levelList'
                      :key='item.value'
                      :label='item.label'
                      :value='item.value' />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
      <div class="bottom">
        <div class="bottom-btn" @click="close">
          取消
        </div>
        <div class="create-btn" @click="handleGenerate">
          <img src='@/assets/digitalbooks/edit/ai-btn.png' />
          立即生成
        </div>
      </div>
    </div>
    <GenerateByAIGC
      ref='generateRef'
      title="生成习题集"
      :content-title="{title0: '已有习题集内容，确定重新生成并覆盖：', title1: '习题集生成提示：'}"
      :content-obj="{content0: '①习题集确定为最终版本', content1: '②生成过程中时间较长，耐心等待'}"
      content-tip="*生成后会删除原习题集内容，不可恢复"
      :data-id='catalogueId'
      :token='token'
      @handleNotify='handleNotify'
    />
    <GenerateNotifByAIGC ref="notifyRef" :generateFun="generateFun" @closeDialog="closeNotify"/>
  </div>
</template>

<script>
import { generateTestpaperByAigc, batchQuestion } from '@/api/digital-api'
import { createTest } from '@/api/test-api'
import GenerateNotifByAIGC from '@/components/classPro/GenerateByAIGC/generateNotifByAIGC'
import GenerateByAIGC from '@/components/classPro/GenerateByAIGC/generateByAIGC'
import axios from 'axios'
import store from '@/store'
import router from '@/router'

export default {
  name: 'AiAddConfig',
  components: { GenerateByAIGC, GenerateNotifByAIGC },
  props: {
    hasData: {
      type: Boolean,
      default: false
    },
    exerciseName: {
      type: String,
      default: ''
    },
    testId: {
      type: String,
      default: '0'
    }
  },
  watch: {
    exerciseName(newVal) {
      this.formData.title = newVal
    }
  },
  data() {
    return {
      token: router.currentRoute.query.token,
      catalogueId: store.state.app.activeCatalogueId,
      wordImg: require('@/assets/digitalbooks/edit/word.png'),
      excelImg: require('@/assets/digitalbooks/edit/excel.png'),
      pptImg: require('@/assets/digitalbooks/edit/ppt.png'),
      pdfImg: require('@/assets/digitalbooks/edit/pdf.png'),
      txtImg: require('@/assets/digitalbooks/edit/txt.png'),
      zipImg: require('@/assets/digitalbooks/edit/zip.png'),
      show: false,
      formData: {
        title: this.exerciseName,
        aigcPrompt: '',
        level: '简单',
        choiceNum: 1,
        fillInTheBlankInputNum: 1,
        simpleChooseNum: 1,
        essayQuestionNum: 1
      },
      fileList: [],
      fileLoading: false,
      levelList: [
        { label: '简单', value: '简单' },
        { label: '中等', value: '中等' },
        { label: '困难', value: '困难' }
      ],
      questionObj: {
        exerciseName: this.exerciseName,
        choiceQuestionList: [],
        essayQuestionList: [],
        fillInTheBlankInputQuestionList: [],
        simpleChooseQuestionList: []
      },
      generateSuccess: true
    }
  },
  methods: {
    open() {
      this.questionObj = {
        exerciseName: this.exerciseName,
        choiceQuestionList: [],
        essayQuestionList: [],
        fillInTheBlankInputQuestionList: [],
        simpleChooseQuestionList: []
      }
      this.formData = {
        title: this.exerciseName,
        aigcPrompt: '',
        level: '简单',
        choiceNum: 1,
        fillInTheBlankInputNum: 1,
        simpleChooseNum: 1,
        essayQuestionNum: 1
      }
      this.generateSuccess = true
      this.show = true
    },
    close() {
      this.show = false
    },
    async beforeUpload (file) {
      if (this.fileList.length >= 10) {
        this.$message.error('最多只能上传10个文件')
        return false
      }
      const isLtM = file.size / 1024 / 1024 < 150
      if (!isLtM) {
        this.$message.warning('上传文件不能超过150M')
        return false
      }
      try {
        this.fileLoading = true
        const formData = new FormData()
        formData.append('file', file)
        const { data } = await axios.post(`${process.env.VUE_APP_BASE_API}/api/v2/comm/vt/uploadAiFile`, formData)
        if (data.code === 200) {
          this.fileList.push({
            name: file.name,
            size: file.size,
            type: file.type,
            fileData: `${data.data}`
          })
        } else {
          this.$message.error(`上传文件${file.name}时${data.message}` || '文件上传失败')
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.fileLoading = false
      }
    },
    officeImageUrl (fileName) {
      if (fileName.includes('doc') || fileName.includes('docx')) {
        return this.wordImg
      } else if (fileName.includes('xls') || fileName.includes('xlsx')) {
        return this.excelImg
      } else if (fileName.includes('ppt') || fileName.includes('pptx')) {
        return this.pptImg
      } else if (fileName.includes('pdf')) {
        return this.pdfImg
      } else if (fileName.includes('txt')) {
        return this.txtImg
      } else if (fileName.includes('zip')) {
        return this.zipImg
      }
    },
    deleteFile (item) {
      const index = this.fileList.findIndex(file => file.fileData === item.fileData)
      if (index !== -1) {
        this.fileList.splice(index, 1)
      }
    },
    handleGenerate() {
      if (this.formData.choiceNum < 1 && this.formData.fillInTheBlankInputNum < 1 && this.formData.simpleChooseNum < 1 && this.formData.essayQuestionNum < 1) {
        this.$message.error('题型数量不能都小于1')
        return
      }
      this.$refs.generateRef.show(this.hasData)
    },
    handleNotify(type) {
      this.$refs.generateRef.close()
      this.$refs.notifyRef.show(type)
    },
    async generateFun() {
      try {
        const params = {
          digitalCatalogueId: this.catalogueId,
          title: this.formData.title,
          aigcPrompt: this.formData.aigcPrompt,
          level: this.formData.level,
          choiceNum: this.formData.choiceNum,
          fillInTheBlankInputNum: this.formData.fillInTheBlankInputNum,
          simpleChooseNum: this.formData.simpleChooseNum,
          essayQuestionNum: this.formData.essayQuestionNum,
          aigcFileIds: this.fileList.reduce((pre, cur) => {
            pre.push(cur.fileData)
            return pre
          }, [])
        }
        this.generateSuccess = true
        const { data } = await generateTestpaperByAigc(params)
        this.questionObj = data
        this.questionObj.exerciseName = this.formData.title
        let testPaperId = this.testId
        if (this.testId === '0' || this.testId === 'undefined') {
          const { data: testData } = await createTest({
            testPaperLinkType: 'DIGITAL_BOOK_CONTENT',
            testPaperTitle: this.formData.title,
            sourceId: this.catalogueId
          })
          this.$emit('updateTestId', testData.id)
          testPaperId = String(testData.id)
        }
        if (!data.essayQuestionList) data.essayQuestionList = []
        if (!data.choiceQuestionList) data.choiceQuestionList = []
        if (!data.fillInTheBlankInputQuestionList) data.fillInTheBlankInputQuestionList = []
        if (!data.simpleChooseQuestionList) data.simpleChooseQuestionList = []
        // debugger
        await batchQuestion({
          id: Number(testPaperId),
          questionList: [...data.choiceQuestionList, ...data.essayQuestionList, ...data.fillInTheBlankInputQuestionList, ...data.simpleChooseQuestionList]
        })
        this.questionObj.exerciseId = this.testId
      } catch (e) {
        this.$refs.notifyRef.close()
        this.generateSuccess = false
        console.log(e)
      }
    },
    closeNotify() {
      this.$refs.notifyRef.close()
      if (this.generateSuccess) {
        this.show = false
        this.$emit('generateSuccess', this.questionObj)
      }
    }
  }
}
</script>

<style scoped lang='scss'>
.no-conversion{
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 101;
  display: flex;
  align-items: center;
  justify-content: center;
  .uploader{
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 130px;
    z-index: 102;
    position: relative;
    .el-upload__title{
      height: 30px;
      line-height: 30px;
      font-size: 14px;
    }
    .mt{
      margin-top: 80px;
    }
    .el-upload__tip{
      height: 20px;
      line-height: 20px;
      font-size: 10px;
      margin-top: 0;
      color: rgba(108, 117, 125, 1);
    }
    .file-list{
      width: 100%;
      height: 80%;
      position: absolute;
      top: 0;
      left: 0;
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      padding: 5px;
      overflow-y: auto;
      .file-item{
        display: flex;
        align-items: center;
        padding: 0 5px;
        height: 25px;
        width: calc(20% - 8px);
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.09);
        border-radius: 4px;
        img{
          width: 20px;
          height: 20px;
          margin-right: 5px;
          object-fit: contain;
        }
        .file-name{
          font-size: 10px;
          color: rgba(51, 51, 51, 1);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          height: 25px;
          line-height: 25px;
          width: calc(100% - 25px - 20px);
        }
        .delete{
          font-size: 14px;
          margin-left: 5px;
          color: rgba(130, 130, 130, 1);
          cursor: pointer;
          &:hover{
            color: #409EFF;
          }
        }
      }
    }
  }
  .ai-main{
    width: 75%;
    max-height: 90vh;
    background-color: rgba(242, 242, 242, 1);
    border-radius: 10px;
    padding: 10px 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    .title{
      background: linear-gradient(90deg, #00C6FF 0%, #0072FF 100%);
      -webkit-background-clip: text;
      color: transparent;
      font-size: 18px;
      font-weight: 600;
      height: 40px;
      width: 120px;
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }
    .bottom{
      width: 100%;
      heigth: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 10px;
      .bottom-btn{
        height: 40px;
        width: 100px;
        padding: 0 15px;
        border-radius: 5px;
        cursor: pointer;
        color: white;
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #d9d9d9;
        color: rgba(130, 130, 130, 1);
        background: white;
        margin-right: 20px;
        &:hover{
          background-color: #c0c0c0;
        }
      }
      .create-btn{
        height: 40px;
        padding: 0 15px;
        border-radius: 5px;
        cursor: pointer;
        color: white;
        font-size: 14px;
        display: flex;
        align-items: center;
        background: linear-gradient(95.99deg, #B721FF -67.63%, #21D4FD 109.16%);
        img{
          width: 16px;
          height: 16px;
          object-fit: cover;
          margin-right: 5px;
        }
      }
    }
    .content{
      width: 100%;
      max-height: calc(90vh - 90px);
      font-size: 16px;
      overflow-y: auto;
      ::v-deep .el-form-item__label{
        padding-bottom: 0;
      }
      ::v-deep .el-form-item{
        margin-bottom: 0;
      }
      ::v-deep .el-upload{
        width: 100%;
        height: 100px;
        cursor: pointer;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
      ::v-deep .el-upload-dragger{
        width: 100%;
        height: 100%;
        border: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
      .content-title{
        font-weight: 500;
      }
      .content-subtitle{
        margin-top: 5px;
        font-size: 14px;
        color: rgba(102, 102, 102, 1);
        margin-bottom: 10px;
      }
      .basic{
        width: 100%;
        padding: 20px ;
        background: white;
        border-radius: 10px;
      }
      .config{
        width: 100%;
        padding: 20px ;
        background: white;
        border-radius: 10px;
        margin-top: 10px;
      }
    }
  }
  .sub-label{
    margin-left: 5px;
    font-size: 12px;
    color: rgba(130, 130, 130, 1);
  }
  .mt10{
    margin-top: 10px;
  }
  ::v-deep .el-form-item__label{
    height: 40px;
    line-height: 40px;
  }
  ::v-deep .el-input-number--small{
    height: 40px;
    line-height: 40px;
    font-size: 14px;
  }
  ::v-deep .el-textarea__inner{
    font-size: 14px;
    padding-left: 10px;
    padding-right: 10px;
  }
  ::v-deep .el-input__inner{
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    padding-left: 10px;
    padding-right: 10px;
  }
}
</style>
