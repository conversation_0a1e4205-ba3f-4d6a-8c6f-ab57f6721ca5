<template>
  <el-dialog
    title="导出Word文档"
    :visible.sync="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    :modal="false"
    custom-class="export-dialog-wrapper"
    @close="handleClose"
  >
    <div class="export-dialog">
      <div class="export-options">
        <el-form :model="exportForm" label-width="120px">
          <el-form-item label="文件名：">
            <el-input
              v-model="exportForm.filename"
              placeholder="请输入文件名"
              :suffix="getFileSuffix()"
            />
          </el-form-item>

          <el-form-item label="内容：">
            <el-radio-group v-model="exportForm.contentMode">
              <el-radio label="questionsOnly">仅题目</el-radio>
              <el-radio label="questionsAndAnswers">题目+答案</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="handleClose">取消</el-button>
      <el-button
        size="small"
        type="primary"
        @click="handleExport"
        :loading="exporting"
      >
        {{ exporting ? '导出中...' : '确认导出' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import DocxExporter from './exportStrategies/docxExporter.js'

export default {
  name: 'ExportDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    exerciseData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      exporting: false,
      exportForm: {
        filename: '',
        contentMode: 'questionsOnly'
      },
      exporters: {}
    }
  },
  created() {
    this.exporters = {
      docx: new DocxExporter()
    }
  },
  computed: {
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.initDialog()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    initDialog() {
      this.exportForm.filename = this.exerciseData.exerciseName || '习题集'
      this.exporting = false
    },

    getFileSuffix() {
      return '.docx'
    },

    async handleExport() {
      try {
        this.exporting = true

        const exporter = this.exporters.docx

        let includeOptions = []
        if (this.exportForm.contentMode === 'questionsAndAnswers') {
          includeOptions = ['answers', 'analysis', 'knowledges', 'scores']
        } else {
          // includeOptions = ['knowledges', 'scores']
          includeOptions = []
        }

        const result = await exporter.export(this.exerciseData, {
          filename: this.exportForm.filename,
          includeOptions: includeOptions,
          contentMode: this.exportForm.contentMode
        })

        // 下载文件
        const link = document.createElement('a')
        link.href = URL.createObjectURL(result.blob)
        link.download = result.filename
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(link.href)

        this.$message.success('导出成功！')
        this.handleClose()
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败：' + error.message)
      } finally {
        this.exporting = false
      }
    },

    handleClose() {
      this.dialogVisible = false
      this.exporting = false
    }
  }
}
</script>

<style scoped lang="scss">
.export-dialog {
  font-size: 13px;

  .export-options {
    padding-top: 20px;

    .el-form {
      .el-form-item {
        margin-bottom: 18px;

        .el-form-item__label {
          font-size: 13px;
          color: #606266;
          line-height: 32px;
        }

        .el-input {
          .el-input__inner {
            font-size: 13px;
            height: 32px;
            line-height: 32px;
          }

          .el-input__suffix {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }

    .content-selector {
      display: flex;
      align-items: center;
      background: #F2F2F2;
      border-radius: 7px;
      padding: 9px 7px;
      box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
      width: fit-content;

      .selector-option {
        font-family: 'PingFang SC', sans-serif;
        font-weight: 400;
        font-size: 14px;
        line-height: 2.93em;
        text-align: center;
        color: #000000;
        cursor: pointer;
        padding: 0 12px;
        transition: all 0.2s ease;
        user-select: none;

        &:hover {
          background: rgba(64, 158, 255, 0.1);
          border-radius: 4px;
        }

        &.active {
          background: #409EFF;
          color: white;
          border-radius: 4px;
        }
      }

      .selector-divider {
        width: 1px;
        height: 20px;
        background: #E0E0E0;
        margin: 0 6px;
      }
    }
  }
}

.dialog-footer {
  text-align: right;

  .el-button {
    padding: 8px 16px;
    font-size: 13px;
    border-radius: 4px;
    min-width: 70px;

    &:not(:last-child) {
      margin-right: 12px;
    }

    &.el-button--small {
      padding: 7px 14px;
      font-size: 12px;
    }
  }
}

::v-deep .export-dialog-wrapper {
  .el-dialog__header {
    padding: 16px 20px 12px;

    .el-dialog__title {
      font-size: 15px;
      font-weight: 500;
      color: #303133;
    }
  }

  .el-dialog__body {
    padding: 12px 20px 20px;
  }

  .el-dialog__footer {
    padding: 12px 20px 16px;
  }
}
</style>
