<template>
  <div class="question-view" :class="{'cursor': !editing, 'question-view-active' : isActive}">
    <div class="drag-handle">
      <img src='@/assets/digitalbooks/edit/drag.png' alt='' />
    </div>
    <div class="header">
      <div class="type-name">
        {{ questionData.questionType === 'CHOICE' ? '选择题' :
          questionData.questionType === 'FILL_IN_THE_BLANK_INPUT' ? '填空题' :
            questionData.questionType === 'SIMPLE_CHOOSE' ? '判断题' :
             questionData.questionType === 'ESSAY_QUESTION' ? '简答题' :
               questionData.questionType || '未知类型'}}
      </div>
      <div class="flex">
        <template v-if="!editing">
          <div class="item-btn" @click="editing = true">
            <i class="el-icon-edit-outline"></i>
            编辑
          </div>
          <div class="item-btn" @click="handleDeleteQuestion">
            <i class="el-icon-delete-solid"></i>
            删除
          </div>
        </template>
        <template v-else>
          <div class="item-btn ai-btn" @click="$refs.addQuestionRef.open(questionData.questionType)">
            <img src='@/assets/digitalbooks/edit/ai-btn.png' />
            AI出题
          </div>
          <div class="item-btn save-btn" @click="handleSave">
            保存
          </div>
          <div class="item-btn" @click="handleCancel">
            取消
          </div>
        </template>
      </div>
    </div>
    <div class="content">
      <div class="title">
        <div class="index-view">{{ indexString }}</div>
        <div class="score-view">
          <span v-if="!editing">{{questionData.score}}分</span>
          <el-input v-else v-model='formData.score' style='width: 100%'/>
        </div>
        <span v-if="!editing">{{formatterFill(questionData.question)}}</span>
<!--        <el-input-->
<!--          v-else-->
<!--          v-model='formData.question'-->
<!--          :placeholder="questionData.questionType === 'FILL_IN_THE_BLANK_INPUT' ? placeholder : '请输入题目'"-->
<!--          style='width: 100%'-->
<!--          type='textarea'-->
<!--          :rows='1'/>-->
        <AutoResizeTextarea
          v-else
          v-model="formData.question"
          :placeholder="questionData.questionType === 'FILL_IN_THE_BLANK_INPUT' ? placeholder : '请输入题目'"
          :min-rows="1"
          :maxlength='300'
        />
      </div>
      <template v-if="!editing">
        <div class="answer-item" v-for="(item, index) in questionData.answerOptionList" :key="index">
          <div class="label">{{getOptionLabel(index)}}</div>
          {{ item.answer }}
        </div>
      </template>
      <template v-if="editing && (questionData.questionType === 'CHOICE' || questionData.questionType === 'SIMPLE_CHOOSE')">
        <div class="answer-item" v-for="(item, index) in formData.answerOptionList" :key="index">
          <div class="label" :class="{'label-right' : item.right}" @click="changeAnswerStatus(item)">{{getOptionLabel(index)}}</div>
          <el-input v-if="questionData.questionType === 'CHOICE'" v-model='item.answer' placeholder='请输入选项' style='width: 80%' maxlength='150' show-word-limit />
          <span v-if="questionData.questionType === 'SIMPLE_CHOOSE'">{{ item.answer }}</span>
          <i
            v-if="questionData.questionType === 'CHOICE'"
            class="el-icon-delete-solid ml10"
            style="cursor: pointer"
            @click="handleDelete(index)"></i>
        </div>
        <div class="add-answer-view" v-if="questionData.questionType === 'CHOICE'">
          <i class="el-icon-plus" style='color: rgba(47, 128, 237, 1)' @click="addAnswer"></i>
        </div>
      </template>
    </div>
    <div class="bottom">
      <div class="flex align-center" v-if="questionData.questionType !== 'ESSAY_QUESTION'">
        <div class="bottom-tip answer-tip">题目答案</div>
        <div class="bottom-content" style="font-weight: 500">
          <span v-if='!editing'>{{ getAnswer(questionData) }}</span>
          <el-input v-if="editing &&  questionData.questionType !== 'CHOICE' && editing &&  questionData.questionType !== 'SIMPLE_CHOOSE'" v-model='formData.answer' placeholder='请输入答案'/>
          <span v-if="editing && (questionData.questionType === 'CHOICE' || questionData.questionType === 'SIMPLE_CHOOSE')">{{ getAnswerFormData(formData) }}</span>
        </div>
      </div>
      <div class="flex align-center">
        <div class="bottom-tip analysis-tip">题目解析</div>
        <div class="bottom-content">
          <span v-if='!editing'>{{ questionData.analysis || '暂无解析' }}</span>
<!--          <el-input v-else v-model='formData.analysis' style='width: 100%' type='textarea' :rows='2' placeholder='请输入解析'/>-->
          <AutoResizeTextarea
            v-else
            v-model="formData.analysis"
            :placeholder="'请输入解析'"
            :min-rows="2"
            :maxlength='500'
          />
        </div>
      </div>
      <div class="flex align-center">
        <div class="bottom-tip point-tip">知识点</div>
        <div v-if='!editing' class="bottom-point">
          <div v-for="(item, index) in questionData.knowledges" :key='index' class="point-item">{{item}}</div>
        </div>
        <div v-else class="bottom-point">
          <div v-for="(item, index) in formData.knowledges" :key='index' class="point-item">
            {{item}}
            <i class='el-icon-error point-delete' @click='formData.knowledges.splice(index, 1)'></i>
          </div>
          <i class='el-icon-plus point-add' @click="addPoint"></i>
        </div>
      </div>
    </div>
    <AddQuestion ref="addQuestionRef" @addQuestion='aiAddQuestion'/>
  </div>
</template>

<script>
import AutoResizeTextarea from '@/components/classPro/AutoResizeTextarea/AutoResizeTextarea'
import AddQuestion from '@/views/digitalbooks/editor/components/addExercise/addQuestion'
import { createTest, question } from '@/api/test-api'
import store from '@/store'
export default {
  name: 'QuestionItem',
  components: { AddQuestion, AutoResizeTextarea },
  props: {
    questionData: {
      type: Object,
      default: () => ({
        question: '',
        questionType: '',
        answer: '',
        analysis: '',
        answerOptionList: [],
        score: 0,
        knowledges: []
      })
    },
    indexString: {
      type: String,
      default: '1.'
    },
    isActive: {
      type: Boolean,
      default: false
    },
    isEditing: {
      type: Boolean,
      default: false
    },
    testId: {
      type: String,
      default: '0'
    },
    testPaperTitle: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      editing: false,
      formData: {},
      placeholder: '请在题干中使用[ ]符号来设置填空题的正确答案，正确答案请勿输入逗号。示例:北京奥云会在[2008]年举行。'
    }
  },
  watch: {
    questionData: {
      handler(newVal) {
        this.formData = JSON.parse(JSON.stringify(newVal))
        if (!this.formData.answerOptionList) {
          this.formData.answerOptionList = []
        }
        if (!this.formData.knowledges) {
          this.formData.knowledges = []
        }
      },
      immediate: true
    },
    isEditing: {
      handler(newVal) {
        this.editing = newVal
      },
      immediate: true
    }
  },
  methods: {
    formatterFill(item) {
      return item.replace(/\[.*?\]/g, '___')
    },
    getOptionLabel(index) {
      const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
      return letters[index] || ''
    },
    getAnswerFormData(formData) {
      return formData.answerOptionList
        .filter((option, index) => {
          option.index = index
          return option.right
        })
        .map(option => this.getOptionLabel(option.index))
        .join(', ')
    },
    getAnswer(question) {
      if (question.questionType === 'CHOICE' || question.questionType === 'SIMPLE_CHOOSE') {
        const list = question.answer.split(',')
        return question.answerOptionList
          .filter((option, index) => {
            option.index = index
            return list.includes(String(option.id))
          })
          .map(option => this.getOptionLabel(option.index))
          .join(', ')
      } else {
        return question.answer || ''
      }
    },
    handleDeleteQuestion() {
      this.$confirm('确定删除该题目吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('delete-question')
      })
    },
    handleCancel() {
      this.editing = false
      this.formData = JSON.parse(JSON.stringify(this.questionData))
    },
    handleDelete(index) {
      if (this.formData.answerOptionList.length > 2) {
        this.formData.answerOptionList.splice(index, 1)
        console.log(this.formData.answerOptionList.length)
      } else {
        this.$message.warning('至少保留两个选项')
      }
    },
    addAnswer() {
      this.formData.answerOptionList.push({
        answer: '',
        right: false
      })
    },
    changeAnswerStatus(item) {
      if (this.questionData.questionType === 'CHOICE') {
        const rightList = this.formData.answerOptionList.filter(i => i.right)
        if (rightList.length === 1 && item.right) {
          this.$message.warning('至少选择一个正确答案')
          return
        }
        item.right = !item.right
      } else {
        this.formData.answerOptionList.forEach(i => {
          i.right = false
        })
        item.right = true
      }
    },
    async handleSave() {
      let testPaperId = this.testId
      if (this.testId === '0') {
        const { data: testData } = await createTest({
          testPaperLinkType: 'DIGITAL_BOOK_CONTENT',
          testPaperTitle: this.testPaperTitle,
          sourceId: store.state.app.activeCatalogueId
        })
        testPaperId = String(testData.id)
        this.$emit('update-test-id', testPaperId)
      }
      const params = {
        apiType: this.formData.id ? 'update' : 'create',
        testPaperId: testPaperId
      }
      await question(params, this.formData)
      // this.$message.success('保存成功')
      this.editing = false
      this.$emit('refresh-questions')
    },
    addPoint() {
      this.$prompt('请输入知识点', '添加知识点', {
        confirmButtonText: '添加',
        cancelButtonText: '取消',
        inputPattern: /^(?!\s*$).+/,
        inputErrorMessage: '知识点不能为空'
      }).then(({ value }) => {
        if (!this.formData.knowledges.includes(value)) {
          this.formData.knowledges.push(value)
        } else {
          this.$message.warning('该知识点已存在')
        }
      }).catch(() => {
        // 取消操作
      })
    },
    aiAddQuestion(data) {
      let id = null
      if (this.formData.id) {
        id = this.formData.id
      }
      this.formData = data
      this.formData.id = id
    }
  }
}
</script>

<style scoped lang='scss'>
.cursor{
  cursor: pointer;
}
.question-view{
  width: 100%;
  min-height: 100px;
  padding: 10px 10px 10px 20px;
  border: 1px solid rgba(224, 224, 224, 1);
  box-shadow: 0px 4px 12px 0px rgba(47, 128, 237, 0.1);
  border-radius: 12px;
  margin-top: 15px;
  position: relative;
  .drag-handle{
    position: absolute;
    width: 30px;
    height: 30px;
    top: 50%;
    left: -10px;
    transform: translateY(-50%);
    cursor: move;
    img{
      width: 30px;
      height: 30px;
      object-fit: contain;
    }
  }
  .header{
    width: 100%;
    height: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .type-name{
      font-size: 16px;
      font-weight: 500;
      color: rgba(47, 128, 237, 1);
      background-color: rgba(230, 240, 255, 1);
      padding: 0 15px;
      border-radius: 15px;
      height: 30px;
    }
    .ai-btn{
      background: linear-gradient(95.99deg, #B721FF -67.63%, #21D4FD 109.16%);
      color: rgba(255, 255, 255, 1) !important;
      border: none;
      cursor: pointer;
      width: 90px !important;
      img{
        width: 20px;
        height: 20px;
        margin-right: 5px;
      }
      &:hover{
        background-color: rgba(30, 100, 200, 1);
      }
    }
    .save-btn{
      background-color: rgba(47, 128, 237, 1);
      color: rgba(255, 255, 255, 1) !important;
      border: none;
      cursor: pointer;
      &:hover{
        background-color: rgba(30, 100, 200, 1);
      }
    }
    .item-btn{
      display: flex;
      align-items: center;
      justify-content: center;
      width: 70px;
      height: 30px;
      font-size: 14px;
      color: rgba(102, 102, 102, 1);
      border-radius: 5px;
      cursor: pointer;
      border: 1px solid rgba(224, 224, 224, 1);
      margin-left: 10px;
      &:hover{
        background-color: rgba(200, 220, 255, 1);
      }
      i{
        font-size: 14px;
        margin-right: 5px;
      }
    }
  }
  .content{
    width: 100%;
    margin-top: 10px;
    .title{
      width: 100%;
      font-size: 14px;
      padding: 0 55px 0 20px;
      position: relative;
      min-height: 25px;
      .index-view{
        position: absolute;
        left: 0;
        top: 0;
        width: 15px;
        height: 100%;
      }
      .score-view{
        position: absolute;
        right: 0;
        top: 0;
        width: 50px;
        height: 25px;
        border: 1px solid rgba(224, 224, 224, 1);
        border-radius: 3px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    .add-answer-view{
      width: 100%;
      height: 30px;
      padding-left: 37px;
      display: flex;
      align-items: center;
      font-size: 16px;
    }
    .answer-item{
      width: 100%;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding-left: 30px;
      font-size: 14px;
      margin-top: 8px;
      .label{
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(243, 243, 243, 1);
        border-radius: 50%;
        margin-right: 10px;
      }
      .label-right{
        background-color: rgba(47, 128, 237, 1);
        color: rgba(255, 255, 255, 1);
      }
    }
  }
  .bottom{
    width: 100%;
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 10px 20px 10px 30px;
    font-size: 12px;
    background: rgba(251, 251, 251, 1);
    border-radius: 10px;
    .bottom-tip{
      width: 70px;
      height: 25px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 5px;
      font-weight: 500;
      flex-shrink: 0;
    }
    .answer-tip{
      background: linear-gradient(90deg, #ABECD6 0%, #FBED96 100%);
    }
    .analysis-tip{
      background: linear-gradient(90deg, #FBC2EB 0%, #A6C1EE 100%);
    }
    .point-tip{
      background: linear-gradient(90deg, #84FAB0 0%, #8FD3F4 100%);
    }
    .bottom-content{
      font-size: 14px;
      margin-left: 10px;
      white-space: pre-wrap;
      line-height: 25px;
      width: 100%;
    }
    .bottom-point{
      font-size: 14px;
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
      margin-left: 10px;
      align-items: center;
      .point-item{
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 8px;
        background: rgba(47, 128, 237, 0.17);
        border: 1px solid rgba(47, 128, 237, 1);
        border-radius: 15px;
        color: rgba(47, 128, 237, 1);
        min-width: 50px;
      }
      .point-delete{
        color: rgba(47, 128, 237, 1);
        margin-left: 5px;
        cursor: pointer;
      }
      .point-add{
        color: rgba(47, 128, 237, 1);
        font-size: 16px;
        cursor: pointer;
      }
    }
  }
  .question-view-active{
    border: 1px solid rgba(45, 156, 219, 1);
    background: rgba(246, 252, 255, 1);
  }
  .ml10{
    margin-left: 10px;
  }
  ::v-deep .el-textarea__inner{
    font-size: 14px;
    padding-left: 10px;
    padding-right: 10px;
  }
  ::v-deep .el-input__inner{
    height: 30px;
    line-height: 30px;
    font-size: 14px;
    padding-left: 10px;
    padding-right: 10px;
  }
  ::v-deep .el-textarea .el-input__count{
    bottom: 3px;
  }
}
</style>
