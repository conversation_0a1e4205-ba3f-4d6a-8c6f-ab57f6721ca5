
import {
  EXERCISE_PROTOCOL,
  formatExerciseData
} from '../protocols/exerciseProtocol'

export default class DocxExporter {
  constructor() {
    this.name = '标准DOCX格式'
    this.extension = '.docx'
    this.mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  }

  async export(exerciseData, options = {}) {
    try {
      const standardData = formatExerciseData(exerciseData)
      const content = await this.generateDocxContent(standardData, options.includeOptions || [], options)
      const filename = `${options.filename || standardData.exerciseName}${this.extension}`

      return {
        blob: content,
        filename,
        mimeType: this.mimeType
      }
    } catch (error) {
      throw new Error(`DOCX格式习题集导出失败: ${error.message}`)
    }
  }

  async generateDocxContent(standardData, includeOptions, options = {}) {
    const { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType } = require('docx')

    const children = []

    children.push(
      new Paragraph({
        children: [
          new TextRun({
            text: standardData.exerciseName,
            bold: true,
            size: 32
          })
        ],
        heading: HeadingLevel.TITLE,
        alignment: AlignmentType.CENTER
      })
    )

    children.push(
      new Paragraph({
        children: [
          new TextRun({
            text: `题目总数: ${standardData.questionList.length}题`,
            size: 20
          })
        ],
        alignment: AlignmentType.CENTER
      })
    )

    children.push(new Paragraph({ children: [] }))

    standardData.questionList.forEach((question, index) => {
      children.push(...this.generateQuestionParagraphs(question, index + 1, includeOptions, { Paragraph, TextRun }))
    })

    const doc = new Document({
      sections: [{
        properties: {},
        children: children
      }],
      creator: '缤果课堂习题集系统',
      title: standardData.exerciseName,
      description: `习题集导出文档，包含${standardData.questionList.length}道题目`,
      subject: '习题集'
    })

    const buffer = await Packer.toBuffer(doc)
    return new Blob([buffer], { type: this.mimeType })
  }

  generateQuestionParagraphs(question, index, includeOptions) {
    const { Paragraph, TextRun } = require('docx')
    const paragraphs = []

    const typeName = EXERCISE_PROTOCOL.QUESTION_TYPES[question.questionType] || question.questionType

    let questionText = question.question
    if (question.questionType === 'FILL_IN_THE_BLANK_INPUT') {
      if (question.answer) {
        const answers = Array.isArray(question.answer) ? question.answer : [question.answer]
        answers.forEach(answer => {
          if (typeof answer === 'string' && answer.trim()) {
            const underline = '_'.repeat(Math.max(4, answer.length))
            const escapedAnswer = answer.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
            questionText = questionText.replace(new RegExp(escapedAnswer, 'g'), underline)
          }
        })
      }

      questionText = questionText.replace(/\[([^\]]+)\]/g, (_, content) => {
        return '_'.repeat(Math.max(4, content.length))
      })

      questionText = questionText.replace(/_{2,}/g, (match) => {
        return '_'.repeat(Math.max(4, match.length))
      })
    }

    paragraphs.push(
      new Paragraph({
        children: [
          new TextRun({
            text: `[${typeName}] ${index}. `,
            bold: true,
            size: 24
          }),
          new TextRun({
            text: questionText,
            size: 24
          }),
          new TextRun({
            text: ` (${question.score}分)`,
            size: 20,
            color: '666666'
          })
        ],
        spacing: { before: 200, after: 100 }
      })
    )

    if (question.answerOptionList && question.answerOptionList.length > 0) {
      if (question.questionType === 'CHOICE') {
        const labels = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H']
        question.answerOptionList.forEach((option, optIndex) => {
          const label = labels[optIndex] || (optIndex + 1)
          paragraphs.push(
            new Paragraph({
              children: [
                new TextRun({
                  text: `${label}. ${option.answer}`,
                  size: 22,
                  // bold: option.right,
                  // color: option.right ? '28a745' : '000000'
                  color: '000000'
                })
              ],
              spacing: { before: 50 }
            })
          )
        })
      } else if (question.questionType === 'SIMPLE_CHOOSE') {
        const labels = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H']
        question.answerOptionList.forEach((option, optIndex) => {
          const label = labels[optIndex] || (optIndex + 1)
          paragraphs.push(
            new Paragraph({
              children: [
                new TextRun({
                  text: `${label}. ${option.answer}`,
                  size: 22,
                  // bold: option.right,
                  // color: option.right ? '28a745' : '000000'
                  color: '000000'
                })
              ],
              spacing: { before: 50 }
            })
          )
        })
      }
    }

    if (includeOptions.includes('answers')) {
      let answerText = question.answer

      if ((question.questionType === 'CHOICE' || question.questionType === 'SIMPLE_CHOOSE') && question.answerOptionList) {
        const labels = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H']
        const correctOptions = []

        question.answerOptionList.forEach((option, index) => {
          if (option.right) {
            correctOptions.push(labels[index] || (index + 1))
          }
        })

        answerText = correctOptions.join(', ')
      }

      paragraphs.push(
        new Paragraph({
          children: [
            new TextRun({
              text: '题目答案: ',
              bold: true,
              size: 22,
              color: '007ACC'
            }),
            new TextRun({
              text: answerText,
              size: 22
            })
          ],
          spacing: { before: 100 }
        })
      )
    }

    // if (includeOptions.includes('analysis') && question.analysis) {
    //   paragraphs.push(
    //     new Paragraph({
    //       children: [
    //         new TextRun({
    //           text: '题目解析: ',
    //           bold: true,
    //           size: 22,
    //           color: 'ffc107'
    //         }),
    //         new TextRun({
    //           text: question.analysis,
    //           size: 22
    //         })
    //       ],
    //       spacing: { before: 100 }
    //     })
    //   )
    // }

    // if (includeOptions.includes('knowledges') && question.knowledges.length > 0) {
    //   paragraphs.push(
    //     new Paragraph({
    //       children: [
    //         new TextRun({
    //           text: '知识点: ',
    //           bold: true,
    //           size: 22,
    //           color: '28a745'
    //         }),
    //         new TextRun({
    //           text: question.knowledges.join(', '),
    //           size: 22
    //         })
    //       ],
    //       spacing: { before: 100 }
    //     })
    //   )
    // }

    paragraphs.push(
      new Paragraph({
        children: [],
        spacing: { before: 200, after: 200 }
      })
    )

    return paragraphs
  }

  async validate(exerciseData) {
    const errors = []
    const warnings = []

    if (!exerciseData.exerciseName || exerciseData.exerciseName.trim() === '') {
      errors.push('习题集名称不能为空')
    }

    if (!exerciseData.questionList || exerciseData.questionList.length === 0) {
      errors.push('习题集中没有题目')
    }

    if (exerciseData.questionList) {
      exerciseData.questionList.forEach((question, index) => {
        if (!question.content || question.content.trim() === '') {
          errors.push(`第${index + 1}题内容不能为空`)
        }

        if (question.questionType === 'CHOICE') {
          if (!question.choiceList || question.choiceList.length < 2) {
            errors.push(`第${index + 1}题（选择题）至少需要2个选项`)
          }
          if (!question.answerList || question.answerList.length === 0) {
            errors.push(`第${index + 1}题（选择题）必须有正确答案`)
          }
        }

        if (question.questionType === 'SIMPLE_CHOOSE') {
          if (!question.choiceList || question.choiceList.length !== 2) {
            errors.push(`第${index + 1}题（判断题）必须有且仅有2个选项`)
          }
          if (!question.answerList || question.answerList.length !== 1) {
            errors.push(`第${index + 1}题（判断题）必须有且仅有1个正确答案`)
          }
        }

        if (question.score && question.score < 0) {
          warnings.push(`第${index + 1}题分值为负数，请确认`)
        }
      })

      if (exerciseData.questionList.length > 100) {
        warnings.push('题目数量较多，导出可能需要较长时间')
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }
}
