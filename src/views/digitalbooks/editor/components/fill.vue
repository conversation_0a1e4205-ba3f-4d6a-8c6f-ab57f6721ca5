<template>
  <div>
    <span v-if='showIndex'>{{ index1+1 }}.</span>
    <span v-for="(part, index) in parts" :key="index">
      <template v-if="isBracketFormat(part)">
        <el-input ref="input" v-model="inputValues[index]" :disabled="testPaperType===1" placeholder="填空" @blur="inputDown" />
      </template>
      <template v-else>
        {{ part }}
      </template>
    </span>
    <span v-if="originalString.score && showScore">({{ originalString.score }})分</span>
    <analysisTestItem v-if='questionStatsItem' :questionStatsItem="questionStatsItem"/>
  </div>
</template>
<script>
import analysisTestItem from './analysisTestItem.vue'
export default {
  components: {
    analysisTestItem
  },
  props: {
    showScore: {
      type: Boolean,
      default: true
    },
    showIndex: {
      type: Boolean,
      default: true
    },
    originalString: {
      type: Object,
      default: null
    },
    testPaperType: {
      type: Number,
      default: 0
    },
    questionStatsItem: {
      type: Object,
      default: null
    },
    index1: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      inputValues: [],
      inputIndexMap: {}
    }
  },
  computed: {
    parts() {
      // console.log(this.originalString.question.split(/\[.*?\]/g))
      // console.log(this.originalString.question.split(/(\[\*\])/))
      // debugger
      // // 将原始字符串拆分成文本和 [*] 部分
      // return this.originalString.question.split(/(\[\*\])/)
      const regex = /(\[.*?\])|([^\[\]]+)/g
      const result = this.originalString.question.match(regex)?.filter(item => item.trim() !== '') || []
      return result
    },
    answerIds() {
      return this.originalString.answer.split(',')
    }
  },
  watch: {
    testPaperType(val) {
      if (val === 0) {
        this.setValue()
        this.setColor()
      }
      if (val === 1) {
        this.setColor()
      }
    },
    originalString: {
      handler() {
        this.setValue()
        this.setColor()
      },
      deep: true
    }
  },
  mounted() {
    this.setValue()
    this.$nextTick(() => {
      this.setColor()
    })
  },
  methods: {
    isBracketFormat(str) {
      const regex = /^\[.*?\]$/
      return regex.test(str)
    },
    setColor() {
      this.$refs.input.forEach((item, index) => {
        const inputElement = this.$refs.input[index].$el.querySelector('input')
        if (this.answerIds[index] !== item.value && this.testPaperType === 1) {
          inputElement.style.color = 'red'
        } else {
          inputElement.style.color = '#409EFF'
        }
      })
    },
    setValue() {
      this.inputValues = []
      const answerIds = this.originalString.answerUser ? this.originalString.answerUser.answerIds.split(',') : []
      let inputIndex = 0
      this.parts.forEach((part, index) => {
        if (this.isBracketFormat(part)) {
          this.inputValues.push(answerIds[inputIndex] || '')
          this.inputIndexMap[index] = inputIndex
          inputIndex++
        } else {
          this.inputValues.push(null)
        }
      })
    },
    inputDown() {
      this.$emit('inputDown', { arr: this.inputValues.filter(elm => elm), item: this.originalString })
    }
  }
}
</script>
  <style scoped lang="scss">
  .el-input{
    width: auto;
  }
 ::v-deep .el-input__inner{
    border-left: none !important;
    border-right: none !important;
    border-top: none !important;
    border-radius: 0% !important;
    color:#409EFF ;
    background: white !important;
    text-align: center;
    font-size: 12px !important;
  }
  ::v-deep .el-input.is-disabled{
    background-color: '' !important;
  }
  /* 你可以在这里添加组件的样式 */
  @media only screen and (max-width:767px){
    ::v-deep .el-input__inner{
      width: auto;
    border-left: none !important;
    border-right: none !important;
    border-top: none !important;
    border-radius: 0% !important;
    color:#409EFF ;
    background: #ffffff !important;
    text-align: center;
    font-size: 34px !important;
  }
  }
  </style>
