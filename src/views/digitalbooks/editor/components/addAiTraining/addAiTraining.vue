<template>
  <div v-if="dialogShow" class="pop_main">
    <div class="header">
      <div class="flex items-center" @click="onCancel">
        <img class="icon" src="@/assets/images/skyclass/arrow-back.png" alt="返回按钮" />
        <span>返回</span>
      </div>
      <el-button :loading="saveLoading" type="primary" class="button" @click="saveTraing">保存</el-button>
    </div>
    <div class="content">
      <el-form ref="Form" :model="ruleForm" :rules="rules" label-width="10%" class="demo-ruleForm">
        <el-form-item label="实验标题" prop="trainingName">
          <el-input v-model="ruleForm.trainingName" maxlength="20" show-word-limit />
        </el-form-item>
        <el-form-item label="实验说明" prop="description">
          <TinymceEditor v-model="ruleForm.description" :init="init" />
        </el-form-item>
        <el-form-item label="实验步骤" prop="step">
          <div class="main">
            <div class="left">
              <draggable
                v-model="editorContents"
                :options="{ handle: '.step-content', filter: '.no-drag', preventOnFilter: false }"
                :move="onMove"
                class="drag-area"
                @end="onEnd"
              >
                <div
                  v-for="(content, index) in editorContents"
                  :key="index"
                  :class="index === tabIndex ? 'active' : ''"
                  class="editor-container"
                  @click="handleFocus(index)"
                >
                  <div class="step-content" :class="{ 'no-drag': content.trainingStepType === 'UPLOAD_RESULT' }">
                    <div class="step-text" v-html="content.instructions"></div>
                    <p class="title">{{ content.trainingStepType === 'UPLOAD_RESULT' ? '实验结果提交配置' : '步骤' + (index + 1) }}</p>
                    <div class="delete_item" @click.stop="removeEditor(index)">
                      <i class="el-icon-error"></i>
                    </div>
                  </div>
                </div>
              </draggable>
              <div class="add_step">
                <el-button type="primary" class="add" @click="addUpload">实验结果提交配置</el-button>
                <el-button type="primary" class="add" @click="addEditor">添加步骤</el-button>
              </div>
            </div>
            <div class="right">
              <div class="right_top">
                <TinymceEditor
                  v-model="editorContents[tabIndex].instructions"
                  :init="init"
                  placeholder="请输入步骤说明"
                  @input="handleInput"
                />
              </div>
              <div v-if="editorContents[tabIndex].trainingStepType !== 'UPLOAD_RESULT'" class="right_bottom">
                <el-radio-group v-model="editorContents[tabIndex].trainingStepType" @change="handleLinkTypeChange">
                  <el-radio :label="'REGULAR_STEP'">不配置</el-radio>
                  <el-radio :label="'AICHAT_STEP'">使用内嵌大语言模型</el-radio>
                  <el-radio :label="'EXTERNAL_LINK_STEP'">配置外部链接</el-radio>
                </el-radio-group>
              </div>
              <el-input
                v-if="editorContents[tabIndex].trainingStepType === 'EXTERNAL_LINK_STEP'"
                v-model="editorContents[tabIndex].externalLink"
                placeholder="请输入URL链接"
                style="margin-top: 10px; margin-bottom: 10px;"
              />
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import TinymceEditor from '@tinymce/tinymce-vue'
import draggable from 'vuedraggable'
import defaultConfig from '../../utils/traningConifg.js'
import { training, trainingStep, getTraining, dragTrainingStep } from '@/api/training-api'
import router from '../../../../../router/index.js'
import store from '../../../../../store/index.js'
import { Message, MessageBox } from 'element-ui'

export default {
  components: { TinymceEditor, draggable },
  data() {
    return {
      cbs: null,
      dialogShow: false,
      token: '',
      init: Object.assign(defaultConfig, {
        min_height: 500,
        plugins: defaultConfig.plugins + ' autoresize',
        max_height: 1000
      }),
      rules: {
        trainingName: [
          { required: true, message: '请输入实验标题', trigger: 'blur' },
          { min: 1, max: 20, message: '长度不能大于20', trigger: 'blur' }
        ]
      },
      ruleForm: {
        trainingName: '',
        description: ''
      },
      isShowUpload: false,
      ossUrl: '',
      tabIndex: 0,
      demo: null,
      saveLoading: false,
      editorContents: [{
        instructions: '',
        trainingStepType: 'REGULAR_STEP',
        externalLink: ''
      }],
      tokenList: {

      }
    }
  },
  mounted() {
    this.token = `Bearer ${router.currentRoute.query.token}`
  },
  methods: {
    onMove(evt) {
      const draggedElement = this.editorContents[evt.draggedContext.index]
      return draggedElement.trainingStepType !== 'UPLOAD_RESULT'
    },
    async onEnd(evt) {
      const oldIndex = evt.oldIndex
      const newIndex = evt.newIndex
      const movedElement = this.editorContents[newIndex]
      const previousElement = newIndex === 0 ? null : this.editorContents[newIndex - 1]

      console.log('Moved Element:', movedElement)
      console.log('Old Index:', oldIndex)
      console.log('New Index:', newIndex)
      console.log('Previous Element:', previousElement)

      try {
        if (!this.ruleForm.trainingId) {
          if (!this.ruleForm.trainingName) {
            Message.warning('请输入实验标题')
            return
          }
          const response = await training({
            ...this.ruleForm,
            linkSourceId: store.state.app.activeCatalogueId,
            trainingLinkType: 'DIGITAL_BOOK_CATALAGUE_CONTENT',
            trainingType: 'COMM_PRACTICE'
          }, { authorization: this.token })

          this.ruleForm.trainingId = response.data.trainingId
        }

        if (!movedElement.trainingStepId) {
          const response = await trainingStep({
            apiType: 'create'
          }, {
            ...movedElement,
            trainingId: this.ruleForm.trainingId
          }, { authorization: this.token })

          movedElement.trainingStepId = response.data.trainingStepId
        }

        if (previousElement && !previousElement.trainingStepId) {
          const response = await trainingStep({
            apiType: 'create'
          }, {
            ...previousElement,
            trainingId: this.ruleForm.trainingId
          }, { authorization: this.token })

          previousElement.trainingStepId = response.data.trainingStepId
        }

        if (this.editorContents.length === 2) {
          // eslint-disable-next-line no-unused-vars
          for (const element of this.editorContents) {
            if (!element.trainingStepId) {
              const response = await trainingStep({
                apiType: 'create'
              }, {
                ...element,
                trainingId: this.ruleForm.trainingId
              }, { authorization: this.token })

              element.trainingStepId = response.data.trainingStepId
            }
          }
        }

        await dragTrainingStep({
          trainingId: movedElement.trainingId,
          trainingStepId: movedElement.trainingStepId,
          previousTrainingStepId: previousElement ? previousElement.trainingStepId : null
        }, { authorization: this.token })

        this.$message.success('步骤顺序更新成功')
      } catch (error) {
        this.$message.error('步骤顺序更新失败')
        console.error(error)
      }
    },
    addUpload() {
      if (!this.isShowUpload) {
        this.editorContents.push({
          instructions: '',
          trainingStepType: 'UPLOAD_RESULT',
          externalLink: '',
          linkType: 'INTERNAL'
        })
        this.isShowUpload = true
      } else {
        this.$message.warning('只能配置一个实验结果提交配置')
      }
    },
    initData() {
      this.tabIndex = 0
      this.editorContents = [{
        instructions: '',
        trainingStepType: 'REGULAR_STEP',
        externalLink: '',
        linkType: 'INTERNAL'
      }]
      this.ruleForm = {
        trainingName: '',
        description: ''
      }
    },
    handleFocus(index) {
      if (index === this.tabIndex) { return }
      this.tabIndex = index
    },
    saveTraing() {
      this.$refs.Form.validate(async (valid) => {
        if (valid) {
          if (this.editorContents.length === 1 && !this.editorContents[0].instructions) {
            Message.warning('请至少添加一个不为空的步骤')
            return
          }
          this.saveLoading = true
          const { data } = await training({ ...this.ruleForm, linkSourceId: store.state.app.activeCatalogueId, trainingLinkType: 'DIGITAL_BOOK_CATALAGUE_CONTENT', trainingType: 'COMM_PRACTICE' }, { authorization: this.token })
          for (let i = 0; i < this.editorContents.length; i++) {
            const parms = this.editorContents[i]
            await trainingStep({
              apiType: parms.trainingStepId ? 'update' : 'create'
            }, {
              ...parms,
              trainingId: data.trainingId
            },
            { authorization: this.token })
          }
          this.onSubmit({ title: '操作实验', subTitle: data.trainingName, id: data.trainingId })
          this.saveLoading = false
          this.initData()
          this.dialogShow = false
        } else {
          return false
        }
      })
    },
    addEditor() {
      const newStep = {
        instructions: '',
        trainingStepType: 'REGULAR_STEP',
        externalLink: '',
        linkType: 'INTERNAL'
      }
      if (this.isShowUpload) {
        this.editorContents.splice(this.editorContents.length - 1, 0, newStep)
      } else {
        this.editorContents.push(newStep)
      }
    },
    removeEditor(index) {
      if (this.editorContents.length === 1) {
        Message.warning('请至少添加一个步骤')
        return
      }
      try {
        MessageBox.confirm('确认删除步骤?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          if (this.editorContents[index].trainingStepId) {
            await trainingStep({
              apiType: 'delete'
            }, {
              trainingStepId: this.editorContents[index].trainingStepId
            }, { authorization: this.token })
          }
          this.$nextTick(() => {
            this.handleFocus(0)
            if (this.editorContents[index].trainingStepType === 'UPLOAD_RESULT') {
              this.isShowUpload = false
            }
            this.editorContents.splice(index, 1)
          })
        }).catch(() => {
          console.log('')
        })
      } catch (error) {
        console.log(error)
      }
    },
    close() {
      this.dialogShow = false
      this.$nextTick(() => {
        this.initData()
      })
      document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '9999'
    },
    open(cbs = {}) {
      this.$nextTick(() => {
        this.dialogShow = true
      })
      this.cbs = cbs
      document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '1'
    },
    onSubmit(data) {
      if (Object.prototype.toString.call(this.cbs['onSubmit']) === '[object Function]') {
        this.cbs['onSubmit'](data)
      }
    },
    isJSON(str) {
      if (typeof str === 'string') {
        try {
          JSON.parse(str)
          return true
        } catch (e) {
          return false
        }
      }
      return false
    },
    async setData(param) {
      document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '9999'
      this.dialogShow = true
      const { data } = await getTraining({
        trainingId: param.id
      }, { authorization: this.token })
      this.ruleForm = {
        ...data
      }
      this.editorContents = data.trainingStepList.map(item => {
        return {
          ...item,
          linkType: 'INTERNAL'
        }
      })
      this.isShowUpload = this.editorContents.some(item => item.trainingStepType === 'UPLOAD_RESULT')
      this.tabIndex = 0
      this.$nextTick(() => {
        if (window.MathJax) {
          window.MathJax.typesetPromise()
        }
      })
    },
    onCancel() {
      try {
        MessageBox.confirm('请确认是否有未保存的修改?', '提示', {
          confirmButtonText: '保存',
          cancelButtonText: '放弃',
          type: 'warning'
        }).then(async () => {
          this.saveTraing()
        }).catch(() => {
          this.close()
        })
      } catch (error) {
        console.log(error)
      }
    },
    handleLinkTypeChange() {
      // Implementation of handleLinkTypeChange method
    },
    handleInput(content) {
      if (window.MathJax) {
        window.MathJax.typesetPromise()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.tooltip-text{
  font-size: 8px;
}
.anser_title{
  cursor: pointer;
  font-size: 12px;
}
.addAnser {
  width: 100px;
  font-size: 12px;
  cursor: pointer;
}

#whiteboard {
  width: 100%;
  height: 100%;
}

.anser_item {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;

  .input1 {
    width: 150px;
  }

  .input2 {
    flex: 1;
  }
}

.pop_main {
  width: 100vw;
  height: 100vh;
  background: #ffffff;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 90;
  overflow: auto;
  @include scrollBar;

  .header {
    width: 100%;
    height: 40px;
    padding: 8px;
    background: #F9F9F9;
    font-size: 12px;
    display: flex;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    justify-content: space-between;

    .icon {
      width: 14px;
      height: 14px;
      cursor: pointer;
      margin-right: 5px;
      cursor: pointer;
    }

    .button {
      width: 50px;
      height: 25px;
      font-size: 12px;
      padding: 5px;
    }
  }

  .content {
    width: 100%;
    padding: 10px;
    margin-top: 40px;

    .main {
      width: 100%;
      height: 500px;
      display: flex;
      justify-content: space-between;

      .left {
        width: 382px;
        height: 481px;
        overflow-x: hidden;
        overflow-y: auto;
        @include scrollBar;
        .add_step{
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 50%;
          margin: 0 auto;
          padding-top: 20px;
          .add{
            font-size: 12px;
            cursor: pointer;
            padding: 5px;
          }
        }
        .editor-container {
          width: 370px;
          min-height: 100px;
          background: #FBFBFB;
          padding: 10px;
          padding-top: 20px;
          border-radius: 5px;
          position: relative;
          margin-top: 10px;
          box-sizing: border-box;
          border: 1px solid #ffffff;

          .title {
            position: absolute;
            font-size: 10px;
            font-weight: bold;
            top: -10px;
            left: 10px;
          }

          .delete_item {
            position: absolute;
            font-size: 14px;
            font-weight: bold;
            top: 0px;
            right: 10px;
            color: #BDBDBD;
            cursor: pointer;
          }
          .step-text {
            width: 100%;
            height: 200px;
            word-break: break-all;
            overflow: hidden;
          }
        }

        .active {
          border: 1px solid #98C4FF;
        }

      }

      .right {
        width: 574px;
        height: 481px;

        .text {
          display: block;
          margin: 0 auto;
          font-size: 12px;
          margin-top: 30%;
        }

        .right_top {
          width: 574px;
          ::v-deep .el-upload {
            width: 100%;
            height: 100%;
          }
        }
        .right_bottom{
          width: 574px;
          height: 80px;
          display: flex;
          justify-content: flex-start;
          gap: 10px;
          align-items: center;
          .el-input{
            width: 300px;
          }
        }
        .button_group {
          width: 100%;
          display: flex;
          justify-content: flex-end;
          margin-top: 5px;

          .button {
            font-size: 12px;
          }
        }

        .input {
          margin-top: 5px;
        }
      }
    }
  }
}
</style>
