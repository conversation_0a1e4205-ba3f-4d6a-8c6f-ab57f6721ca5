<template>
  <div v-if="dialogShow" class="pop-body">
    <div class="pop-main" v-loading="saveLoading">
      <div class="pop-title">
        Python实训
        <i class="el-icon-close icon-close" @click="close"></i>
      </div>
      <div class="pop-content">
        <el-form ref="Form" :model='formData' :rules='rules' label-width='18%' label-position='left'>
          <el-form-item label="实验标题" prop="trainingName">
            <el-input v-model="formData.trainingName" maxlength="30" show-word-limit placeholder="请输入实验标题"/>
          </el-form-item>
          <el-form-item label="实验简介">
            <el-input v-model="formData.description" maxlength="200" show-word-limit type='textarea' :rows='5' placeholder="请输入实验简介"/>
          </el-form-item>
          <el-form-item label="预制文件">
            <el-upload
              class="uploader"
              action=""
              :show-file-list="false"
              :before-upload="beforeUpload"
              style='width: 100%'
              accept=".zip"
            >
              <el-button type='primary' size='mini' :loading='fileLoading'>点击上传文件</el-button>
              <div slot="tip" class='upload-tip'>
                点击上传预制Python文件（先压缩成zip包再上传）
              </div>
            </el-upload>
          </el-form-item>
          <div class="file-view" v-for='item in fileList' :key='item.url'>
            {{item.name}}
            <i class="el-icon-delete" style='color: red;cursor: pointer;margin-left: 5px' @click="fileList = []"></i>
          </div>
        </el-form>
      </div>
      <div class="pop-bottom">
        <el-button type='primary' size='mini' class='bottom-btn' @click="handleConfirm">提交</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { getFileUploadAuthor } from '@/api/user-api'
import axios from 'axios'
import { getTraining, training } from '@/api/training-api'
import store from '@/store'
import router from '@/router'

export default {
  name: 'AddPythonTraining',
  data() {
    return {
      dialogShow: false,
      cbs: {},
      formData: {
        trainingName: '',
        description: ''
      },
      rules: {
        trainingName: [
          { required: true, message: '请输入实验标题', trigger: 'blur' },
          { min: 1, max: 30, message: '长度不能大于30', trigger: 'blur' }
        ]
      },
      fileList: [],
      fileLoading: false,
      saveLoading: false,
      token: ''
    }
  },
  mounted() {
    this.token = `Bearer ${router.currentRoute.query.token}`
  },
  methods: {
    initData() {
      this.formData = {
        trainingName: '',
        description: ''
      }
      this.fileList = []
      this.fileLoading = false
    },
    async setData(param) {
      document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '9999'
      this.dialogShow = true
      const { data } = await getTraining({
        trainingId: param.id
      }, { authorization: this.token })
      this.formData = {
        ...data
      }
      if (param.trainingData && param.trainingData.fileUrl) {
        const fileData = JSON.parse(param.trainingData.fileUrl)
        this.fileList = [{
          name: fileData.directory_name,
          url: fileData.download_url
        }]
      } else {
        this.fileList = []
      }
    },
    open(cbs = {}) {
      this.$nextTick(() => {
        this.dialogShow = true
      })
      this.cbs = cbs
      document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '1'
    },
    onSubmit(data) {
      if (Object.prototype.toString.call(this.cbs['onSubmit']) === '[object Function]') {
        this.cbs['onSubmit'](data)
      }
    },
    close() {
      this.dialogShow = false
      this.$nextTick(() => {
        this.initData()
      })
      document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '9999'
    },
    async beforeUpload (file) {
      try {
        this.fileLoading = true
        const { data } = await getFileUploadAuthor({
          mediaType: 'FILE',
          contentType: '',
          quantity: 1,
          fileName: file.name
        })
        this.ossUrl = data[0].ossConfig.host
        const formData = new FormData()
        formData.append('success_action_status', '200')
        formData.append('callback', '')
        formData.append('key', data[0].fileName)
        formData.append('policy', data[0].policy)
        formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
        formData.append('signature', data[0].signature)
        formData.append('file', file)
        await axios.post(this.ossUrl, formData)
        this.fileList = []
        this.fileList.push({
          name: file.name,
          url: `${this.ossUrl}/${data[0].fileName}`
        })
      } catch (e) {
        console.log(e)
      } finally {
        this.fileLoading = false
      }
    },
    async handleConfirm() {
      this.$refs.Form.validate(async (valid) => {
        if (valid) {
          try {
            this.saveLoading = true
            const trainingData = this.fileList.length > 0 ? {
              sourceType: 'TRAINING_PYTHON_PRACTICE',
              fileUrl: JSON.stringify({
                download_url: this.fileList[0].url,
                directory_name: this.fileList[0].name
              })
            } : null
            const { data } = await training({
              linkSourceId: store.state.app.activeCatalogueId,
              trainingLinkType: 'DIGITAL_BOOK_CATALAGUE_CONTENT',
              trainingType: 'PYTHON_PRACTICE',
              trainingName: this.formData.trainingName,
              description: this.formData.description,
              trainingData: trainingData,
              trainingId: this.formData.trainingId || null
            },{ authorization: this.token })
            this.onSubmit({ title: 'python实训', subTitle: data.trainingName, id: data.trainingId, data, trainingData })
            this.initData()
            this.dialogShow = false
          } catch (e) {
            console.log(e)
          } finally {
            this.saveLoading = false
          }
        }
      })
    }
  }
}
</script>

<style scoped lang='scss'>
.pop-body{
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  ::v-deep .el-form-item__label{
    text-align: right;
  }
  .pop-main{
    width: 50%;
    background-color: white;
    border-radius: 10px;
    padding-bottom: 20px;
    .pop-title{
      width: 100%;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      border-bottom: 1px solid #e0e0e0;
      .icon-close{
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 16px;
        cursor: pointer;
        &:hover{
          color: #409EFF;
        }
      }
    }
    .pop-content{
      width: 100%;
      padding: 20px;
      overflow-y: auto;
      .upload-tip{
        width: 100%;
        height: 20px;
        font-size: 10px;
        color: #8c939d;
        margin-top: -20px;
        display: flex;
        padding-top: 5px;
      }
      .file-view{
        padding-left: 18%;
        width: 100%;
        height: 20px;
        display: flex;
        align-items: center;
        font-size: 16px;
      }
    }
    .pop-bottom{
      width: 100%;
      display: flex;
      justify-content: center;
      .bottom-btn{
        width: 200px;
      }
    }
  }
}
</style>
