<template>
  <NormalDialog
    v-if="dialogShow"
    :title="title"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="flex flex-col editor-dig w">
      <audio :src='url' controls></audio>
    </div>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
export default {
  components: { NormalDialog },
  data () {
    return {
      dialogShow: false,
      title: '查看音频',
      url: ''
    }
  },
  mounted () {
  },
  methods: {
    close () {
      this.url = ''
      this.dialogShow = false
    },
    open (url) {
      this.url = url
      this.dialogShow = true
    }
  }
}
</script>

<style lang="scss" scoped>

.editor-dig {
  .content{
    display: flex;
    justify-content: space-between;
    width: 100%;
    .middle{
      width: 100%;
      overflow: hidden;
      text-align: center;
      video{
        height:700px;
        display:block;
        margin: 0 auto;
      }
    }
  }
}
</style>
