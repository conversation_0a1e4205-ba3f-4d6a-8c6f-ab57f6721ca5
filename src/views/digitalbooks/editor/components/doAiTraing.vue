<template>
  <div v-if="showTraing" class="traing_main">
    <div class="head-box">
      <img v-if="type !== 'iframe'" class="back" src="@/assets/digitalbooks/arrow-left.svg" @click="back" />
      <div class="head-title article-singer-container">
        {{ traingData && traingData.trainingName }}
      </div>
      <div class="share" @click="openPop">实验说明</div>
    </div>
    <div class="main">
      <img
        v-if="isCop"
        class="close"
        src="../../../../assets/digitalbooks//read/close.png"
        alt=""
        @click="isCop = false"
      />
      <div class="left" :class="isCop ? 'min-width' : 'cop'">
        <div v-if="isCop && traingData">
          <div
            v-for="(content, index) in traingData && traingData.trainingStepList"
            :key="index"
            :ref="content"
            :class="index === tabIndex ? 'active' : ''"
            class="step_item"
            @click="handleFocus($event,index)"
          >
            <div class="title">
              <div class="idot">{{ index + 1 }}</div>
              <div
                class="type"
                :class="content.userTrainingStep && content.userTrainingStep.trainingStatus === 'FINISHED' ? '' : 'no_fininsh'"
              >
                {{ content.userTrainingStep && content.userTrainingStep.trainingStatus === 'FINISHED' ? '已完成' : '未完成' }}
              </div>
            </div>
            <div v-if="content.trainingStepType !== 'UPLOAD_RESULT'" class="content" :style="{ height: content.isCop ? 'auto' : '' }" v-html="content.stepName||content.instructions"></div>
            <div v-if="content.trainingStepType === 'UPLOAD_RESULT'" class="content" v-html="content.instructions||'上传实验后最终结果文档，格式可上传 Word、PDF、png、mp4、zip等文件'"></div>
            <img
              v-if="index !==traingData.trainingStepList.length-1"
              class="down no_show"
              src="../../../../assets/digitalbooks//read/down.png"
              alt=""
            />
          </div>
        </div>
        <div v-else class="open">
          <img src="../../../../assets/digitalbooks//read/open.png" alt="" @click="isCop = true" />
        </div>
      </div>
      <div v-if="traingData&&traingData.trainingStepList[tabIndex].trainingStepType !== 'UPLOAD_RESULT'" class="right">
        <div @click="readFunc" class="right_top">
          <div
            v-show="traingData&&traingData.trainingStepList[tabIndex].instructions"
            id="whiteboard"
            ref="excel"
            v-loading="loading"
            element-loading-text="数据加载中..."
            class="excel"
            :style="{ height: traingData&&traingData.trainingStepList[tabIndex].externalLink ? '88%' : '100%' }"
            v-html="traingData&&traingData.trainingStepList[tabIndex].instructions"
          >
          </div>
          <div v-show="!traingData || !traingData.trainingStepList[tabIndex].instructions" class="emty">
            <Empty description="暂无数据" />
          </div>
        </div>
        <div class="right_bottom">
          <div v-if="traingData&&traingData.trainingStepList[tabIndex].trainingStepType !== 'REGULAR_STEP'" class="button-container">
            <el-button :disabled="!token" type="primary" class="external-link-btn" @click="openExternalLink(traingData.trainingStepList[tabIndex].externalLink,traingData.trainingStepList[tabIndex].trainingStepType)">
              {{ traingData&&traingData.trainingStepList[tabIndex].trainingStepType==='AICHAT_STEP'?'进入实验':'打开实验连接' }}
            </el-button>
            <div v-if="traingData&&traingData.trainingStepList[tabIndex].trainingStepType!=='AICHAT_STEP'" class="tip-text">该实验需要跳转到外部，点击按钮进入</div>
          </div>
          <el-button :disabled="!token" class="button1" type="primary" @click="submitTraing">确认完成</el-button>
        </div>
      </div>
      <div v-else class="right">
        <div class="right_upload">
          <div v-if="!traingData || !traingData.trainingStepList[tabIndex].instructions" class="upload_title">
            <p>上传实验后最终结果文档，格式可上传 Word、PDF、png、mp4、zip等文件</p>
          </div>
          <div v-else class="upload_title" v-html="traingData.trainingStepList[tabIndex].instructions"></div>
          <div class="upload_content">
            <div class="upload_left">
              <div class="upload-area">
                <el-button :disabled="!token" type="text" icon="el-icon-plus" @click="uploadFile">上传文件</el-button>
                <div class="upload-tip">
                  按照上传要求,上传实验结果
                  <p>上传单个文件不能超过1G,其中文档不能超过200M。</p>
                </div>
              </div>
            </div>
            <div class="upload_right">
              <div v-if="fileList.length" class="file-list">
                <div v-for="(file, index) in fileList" :key="index" class="file-item">
                  <span class="file-name">{{ file.fileName?file.fileName+'.'+file.expendType : file.name }}</span>
                  <span class="file-size">{{ formatFileSize(file.size) }}</span>
                  <div v-if="file.status === 'uploading'" class="progress">
                    <el-progress :percentage="file.percentage" />
                  </div>
                  <div class="operations">
                    <span v-if="file.status === 'success'||file.updatedAt" class="preview" @click="previewFile(file)">预览</span>
                    <span class="delete" @click="removeFile(index)">删除</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <infoPop ref="infoPop" />
    <imgGroupPop ref="imgs" :info="imgListInfo" />
    <officeView ref="officeView" :url="officeUrl" :ctoken="token" />
    <videoCardPop ref="videoCard" :info="videoInfo" />
    <AudioPop ref="audioRef" />
    <AiChat v-if="traingData" ref="aiChat" :info="traingData.trainingStepList[tabIndex].instructions" :index="tabIndex" :training-id="traingData.trainingId" @close="closeAiChat" />
  </div>
</template>
<script>
import AudioPop from '@/views/digitalbooks/editor/components/audioPop'
import { getTraining, updateTrainingStepProgress, updateTrainingResult } from '@/api/training-api'
import { isFileSizeGreaterThan200MB, getFileType, throttle } from '@/utils/index'
import Empty from '@/components/classPro/Empty/index.vue'
import infoPop from './traningToast.vue'
import { getToken } from '@/utils/auth'
import imgGroupPop from './imgGroupPop.vue'
import officeView from '../components/officeView.vue'
import axios from 'axios'
import { saveAs } from 'file-saver'
import { Notification } from 'element-ui'
import { getFileUploadAuthor } from '@/api/user-api'
import { saveMediaFile } from '@/api/digital-api'
import videoCardPop from './videoPop.vue'
import AiChat from './AiChat/index.vue'
export default {
  components: {
    Empty,
    infoPop,
    imgGroupPop,
    officeView,
    videoCardPop,
    AiChat,
    AudioPop
  },
  props: {
    studentCourseId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      showTraing: false,
      traingData: null,
      tabIndex: 0,
      token: '',
      isCop: true,
      loading: false,
      imgListInfo: null,
      fileList: [],
      officeUrl: '',
      videoInfo: {
        src: '',
        poster: '',
        text: ''
      },
      type: ''
    }
  },
  mounted() {
    let tokenQuery = this.$route.query && this.$route.query.token ? this.$route.query.token : ''
    if (tokenQuery && tokenQuery.startsWith('Bearer ')) {
      tokenQuery = tokenQuery.replace('Bearer ', '')
    }
    this.token = this.$route.query && this.$route.query.token ? `Bearer ${tokenQuery}` : getToken()
    if (this.$route.query && (this.$route.query.type === 'iframe' || this.$route.query.type === 'phone-iframe')) {
      this.type = this.$route.query.type
      this.open(this.$route.params.id)
    }
  },
  methods: {
    openAiChat() {
      this.$nextTick(() => {
        this.$refs.aiChat.open()
      })
    },
    closeAiChat() {
      this.showAiChat = false
    },
    async _updateTrainingResult() {
      await updateTrainingResult({
        trainingStepId: this.traingData.trainingStepList[this.tabIndex].trainingStepId,
        studentCourseId: this.studentCourseId,
        resultFileIdList: this.fileList.map(file => file.id).join(',')
      }, { authorization: this.token })
      await this._getTraining(this.traingData.trainingId)
    },
    openExternalLink(url, type) {
      if (!url) {
        this.openAiChat()
        return
      }
      if (type === 'AICHAT_STEP') {
        this.openAiChat()
        return
      }
      let finalUrl = url
      // 检查 URL 是否包含协议
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        finalUrl = `http://${url}` // 如果没有协议，则默认为 http:// 开头
      }
      if (this.type === 'iframe') {
        window.parent.postMessage({
          type: 'iframeOpen',
          url: finalUrl
        }, '*')
      } else {
        window.open(finalUrl, '_blank', 'noopener,noreferrer')
      }
    },
    async submitTraing() {
      await updateTrainingStepProgress({
        trainingStepId: this.traingData.trainingStepList[this.tabIndex].trainingStepId,
        studentCourseId: this.studentCourseId
      }, { authorization: this.token })
      this._getTraining(this.traingData.trainingId)
    },
    previewFile(file) {
      let url = file.url
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = `https://${url}`
      }

      const fileName = file.name ? file.name.toLowerCase() : file.fileName + '.' + file.expendType.toLowerCase()

      // 检查是否为Office文件或PDF
      const officeExtensions = [
        '.doc', '.docx', // Word
        '.xls', '.xlsx', // Excel
        '.ppt', '.pptx', // PowerPoint
        '.vsd', '.vsdx', // Visio
        '.pot', '.potx', // PowerPoint 模板
        '.xlt', '.xltx', // Excel 模板
        '.dot', '.dotx', // Word 模板
        '.pdf' // PDF文件
      ]

      // 检查是否为图片文件
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']

      // 检查是否为视频文件
      const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov', '.avi']
      // 检查是否为音频文件
      const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a']

      if (officeExtensions.some(ext => fileName.endsWith(ext))) {
        // Office文件和PDF预览
        this.officeUrl = url
        this.$nextTick(() => {
          this.$refs.officeView.open()
        })
      } else if (imageExtensions.some(ext => fileName.endsWith(ext))) {
        // 图片文件预览
        this.imgListInfo = {
          content: [{
            src: url,
            info: fileName
          }]
        }
        this.$refs.imgs.open()
      } else if (videoExtensions.some(ext => fileName.endsWith(ext))) {
        // 视频文件预览
        this.videoInfo = {
          src: url,
          poster: '', // 可选的视频封面图
          text: fileName
        }
        this.$refs.videoCard.open()
      } else if (audioExtensions.some(ext => fileName.endsWith(ext))) {
        // 音频文件预览
        this.$refs.audioRef.open(url)
      } else {
        this.$message.error('暂不支持预览该文件类型')
      }
    },
    async _getTraining(id) {
      const { data } = await getTraining({
        trainingId: id,
        studentCourseId: this.studentCourseId
      }, { authorization: this.token })
      if (!data) {
        this.$message.warning('该实验已被删除')
        this.back()
        return
      }
      const Trandata = data.trainingStepList.map(item => {
        return {
          ...item,
          isCop: false
        }
      })
      this.traingData = {
        ...data,
        trainingStepList: Trandata
      }
      this.fileList = data.userTrainingData.resultFileList || []
      this.$nextTick(() => {
        window.MathJax.typesetPromise()
      })
    },

    handleFocus(event, index, flag = false) {
      if (index === this.tabIndex && !flag) {
        if (event.target.tagName === 'IMG' && !event.target.classList.contains('no_show')) {
          this.imgListInfo = {
            content: [{
              src: event.target.currentSrc,
              info: ''
            }]
          }
          this.$refs.imgs.open()
        }
        return
      }
      this.tabIndex = index
      this.$nextTick(() => {
        window.MathJax.typesetPromise()
      })
    },

    async open(id) {
      this.showTraing = true
      await this._getTraining(id)
      this.tabIndex = 1
      this.handleFocus(null, 0)
    },

    back() {
      this.showTraing = false
      this.traingData = null
      this.$emit('close')
      if (this.type === 'phone-iframe'){
        window.parent.postMessage({
          type: 'gameBack'
        }, '*')
      }
    },

    openPop() {
      this.$refs.infoPop.open(this.traingData.description, '实验说明')
    },

    async uploadFile() {
      const input = document.createElement('input')
      input.type = 'file'
      input.addEventListener('change', async (e) => {
        const file = e.target.files[0]
        // 检查文件大小上限 1G
        if (file.size / 1024 / 1024 / 1024 > 1) {
          this.$message.error('上传文件大小不能超过1G')
          return
        }
        // 检查 Office 文件类型大小上限 200M
        const officeExtensions = [
          '.doc', '.docx', // Word
          '.xls', '.xlsx', // Excel
          '.ppt', '.pptx', // PowerPoint
          '.vsd', '.vsdx', // Visio
          '.pot', '.potx', // PowerPoint 模板
          '.xlt', '.xltx', // Excel 模板
          '.dot', '.dotx' // Word 模板
        ]

        if (officeExtensions.some(ext => file.name.toLowerCase().endsWith(ext)) &&
           file.size / 1024 / 1024 > 200) {
          this.$message.error('Office文档大小不能超过200M')
          return
        }

        const { data } = await getFileUploadAuthor({
          mediaType: 'FILE',
          contentType: '',
          quantity: 1,
          fileName: file.name
        })

        const ossCDN = data[0].ossConfig.ossCDN

        try {
          const formData = new FormData()
          formData.append('success_action_status', '200')
          formData.append('callback', '')
          formData.append('key', data[0].fileName)
          formData.append('policy', data[0].policy)
          formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
          formData.append('signature', data[0].signature)
          formData.append('file', file)

          const fileItem = {
            name: file.name,
            size: file.size,
            status: 'uploading',
            percentage: 0
          }
          this.fileList.push(fileItem)
          const fileIndex = this.fileList.length - 1

          const notif = Notification({
            title: `${file.name}上传中`,
            message: '0%',
            duration: 0
          })

          await axios.post(data[0].ossConfig.host, formData, {
            onUploadProgress: (progress) => {
              const complete = Math.floor(progress.loaded / progress.total * 100)
              notif.message = complete + '%'
              this.fileList[fileIndex].percentage = complete

              if (complete >= 100) {
                notif.close()
                this.fileList[fileIndex].status = 'success'
                this.fileList[fileIndex].url = `${ossCDN}/${data[0].fileName}`
              }
            }
          })
          const res = await saveMediaFile({
            fileName: file.name,
            url: data[0].fileName,
            type: 'FILE'
          }, {
            size: Math.floor(file.size / 1024),
            type: 'FILE',
            fileName: file.name.substring(0, file.name.lastIndexOf('.')),
            url: data[0].fileName,
            expendType: file.name.substring(file.name.lastIndexOf('.') + 1)
          }, { authorization: this.token }
          )
          this.fileList[fileIndex].id = res.data.id
          this.fileList[fileIndex].url = `${ossCDN}/${data[0].fileName}`
          this._updateTrainingResult()
        } catch (error) {
          console.log(error)
          this.$message.error('上传失败')
        }
      }, false)

      input.click()
    },

    removeFile(index) {
      this.$confirm('确定删除该文件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.fileList.splice(index, 1)
        this._updateTrainingResult()
        this.$message.success('删除成功')
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    formatFileSize(size) {
      if (size < 1024) {
        return size + 'B'
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(1) + 'KB'
      } else {
        return (size / 1024 / 1024).toFixed(1) + 'M'
      }
    },

    readFunc(e) {
      if (e.target.classList.contains('show_button')) {
        const item = e.target
        this.previewFun(item)
      } else if (e.target.classList.contains('download_button')) {
        const url = e.target.parentNode.children[1].innerText
        const fileName = e.target.parentNode.children[2].innerText
        const notif = Notification({
          title: fileName,
          dangerouslyUseHTMLString: true,
          message: '',
          duration: 0
        })
        throttle(function () {
          const xhr = new XMLHttpRequest()
          xhr.open('get', url)
          xhr.responseType = 'blob'
          xhr.addEventListener('progress', (e) => {
            const complete = Math.floor(e.loaded / e.total * 100)
            notif.message = complete + '%'
            if (complete >= 100) {
              notif.close()
            }
          })
          xhr.send()
          xhr.onload = function () {
            if (this.status === 200 || this.status === 304) {
              const blob = new Blob([this.response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8' })
              saveAs(blob, fileName)
            }
          }
        }, 2000)
      }
      if (this.type === 'iframe' && e.target.tagName === 'A') {
        e.preventDefault()
        const url = e.target.href
        window.parent.postMessage({
          type: 'iframeOpen',
          url: url
        }, '*')
      }
    },
    previewFun(item) {
      const url = item.parentNode.children[1].innerText
      const fileName = item.parentNode.children[2].innerText
      const size = item.parentNode.parentNode.children[2].children[1].innerText
      if (getFileType(fileName) === 'video') {
        this.videoInfo = {
          src: url,
          poster: '',
          text: ''
        }
        this.$refs.videoCard.open()
      } else if (getFileType(fileName) === 'Office') {
        if (isFileSizeGreaterThan200MB(size)) {
          this.$message.warning('暂不支持大于200M的附件预览')
          return
        }
        this.officeUrl = url
        this.$nextTick(() => {
          this.$refs.officeView.open()
        })
      } else if (getFileType(fileName) === 'img') {
        this.imgListInfo = { content: [{ src: url, info: '' }] }
        this.$refs.imgs.open()
      } else if (getFileType(fileName) === '音频') {
        this.$refs.audioRef.open(url)
      } else {
        this.$message.warning('该类型文件暂不支持预览')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-loading-mask{
  background-color: rgba(255, 255, 255, 0.4)
}

.traing_main {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 100;
  overflow: auto;
  background: #F1F7FF;
  padding: 10px;

  .head-box {
    height: 10px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    margin-top: 10px;
    position: relative;

    .share {
      position: absolute;
      right: 40px;
      font-size: 12px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      text-decoration: underline
    }

    .back {
      width: 20px;
      height: 20px;
      object-fit: cover;
      cursor: pointer;
    }

    .head-title {
      color: #000;
      font-size: 12px;
      font-weight: 500;
      margin-left: 10px;
    }
  }

  .main {
    width: 100%;
    height: 93%;
    display: flex;
    position: relative;
    justify-content: space-between;

    .close {
      width: 12px;
      position: absolute;
      left: 295px;
      top: 50%;
      cursor: pointer;
    }
.min-width{
  min-width: 27%;
}
    .left {
      width: 400px;
      background: #FFFFFFBA;
      border-radius: 10px;
      margin-right: 30px;
      padding: 10px;
      overflow-y: auto;
      position: relative;
      @include scrollBar;

      .up {
        margin-top: 4px;
        font-size: 8px;
        font-weight: bold;
        text-align: right;
        padding-right: 5px;
        padding-bottom: 5px;

        span {
          cursor: pointer;
        }
      }

      .step_item {
        width: 100%;
        background: #F9F9F9;
        border-radius: 5px;
        margin-bottom: 24px;
        position: relative;
        ::v-deep img{
          cursor: pointer;
        }
        .title {
          width: 100%;
          height: 30px;
          display: flex;
          justify-content: space-between;
          padding: 5px;

          .idot {
            width: 20px;
            height: 20px;
            line-height: 20px;
            text-align: center;
            border-radius: 10px;
            font-size: 12px;
            font-weight: bold;
            background: linear-gradient(90deg, #4FACFE 0%, #00F2FE 100%);

          }

          .type {
            width: 40px;
            height: 20px;
            text-align: center;
            line-height: 20px;
            border-radius: 5px;
            font-size: 10px;
            font-weight: bold;
            color: #ffffff;
            background: #2D9CDB;
          }

          .no_fininsh {
            background: #EB5757;
          }
        }

        .content {
          padding: 5px;
          min-height: 50px;
          height: 50px;
          overflow: hidden;
          overflow-x: hidden;
          transition: height 1s linear;

          ::v-deep * {
            max-width: 100%;
          }

          ::v-deep pre>code {
            display: block;
            font-size: 12px;
            white-space: pre-wrap;
            word-break: break-all;
          }
        }

        .down {
          width: 15px;
          position: absolute;
          left: 50%;
          bottom: -20px;
        }
      }

      .active {
        background: #D2EFFF;
      }
    }

    .cop {
      width: 20px;
      margin-right: 5px;
    }

    .open {
      width: 100%;
      height: 100%;
      position: relative;

      img {
        width: 15px;
        position: absolute;
        left: -7px;
        top: 50%;
        cursor: pointer;
      }
    }

    .right {
      width: 100%;
      height: 100%;
      position: relative;
      background: #FFFFFFBA;
      border-radius: 10px;
      .right_top {
        width: 100%;
        padding: 10px;
        height: 92%;
      }

      .right_bottom{
        width: 100%;
        margin-top: 50px;
        border-radius: 10px;
        position: relative;
        .button1{
          font-size: 12px;
          font-weight: bold;
        }

      }
      .button-container {
          position: absolute;
          bottom: 20px;
          left: 20px;
          .external-link-btn {
            width: 80px;
            height: 30px;
            font-size: 12px;
            font-weight: bold;
            padding: 0;
          }

          .tip-text {
            font-size: 10px;
            color: #909399;
            position: absolute;
            bottom: 0;
            left: 101%;
            white-space: nowrap;
          }
        }
      .right_upload{
        width: 100%;
        height: 100%;
        background: #FFFFFFBA;
        overflow: hidden;
        border-radius: 10px;
        .upload_title{
          width: 100%;
          height: 60%;
          padding: 10px;
          overflow: auto;
          @include scrollBar;
        }
      }
      .start {
        position: absolute;
        padding: 10px;
        width: 80px;
        font-size: 10px;
        right: 30px;
        bottom: 24vh;
      }
      .restart{
        position: absolute;
        padding: 10px;
        width: 80px;
        font-size: 10px;
        right: 30px;
        bottom: 18vh;
      }
      .title {
        font-size: 12px;
        font-weight: bold;
        margin-bottom: 10px;
      }

      .excel {
        width: 100%;
        height: 88%;
        overflow: auto;
        margin-bottom: 10px;
        @include scrollBar;
      }

        .result {
          width: 20px;
          height: 20px;

          img {
            width: auto;
            height: 18px;
            margin-left: 10px;
          }

          margin-top: 3px;
        }

        .el-input {
          width: 70%;
        }

        ::v-deep .el-input__inner {
          width: 100%;
          height: 30px;
          font-size: 12px;
        }

        .button1 {
          padding: 0;
          width: 80px;
          height: 30px;
          font-size: 10px;
          position: absolute;
          right: 20px;
          bottom:20px;
        }
        .button2{
          position: absolute;
          right: 120px;
          bottom:0px;
          font-size: 10px;
          text-decoration: underline;
        }
      }

      .check {
        width: 100%;
        height: 75%;
        flex-wrap: wrap;
        overflow: auto;
        @include scrollBar;

        .formItem {
          display: flex;
          gap: 10px;
          padding-right: 10px;
          justify-content: space-between;
          line-height: 20px;
          height: auto;
          margin-top: 5px;
          span{
            white-space: nowrap;
            width: 50px;
          }
          .label{
            width: 200px;
            font-size: 10px;
            text-align: right;
            white-space: pre-wrap;
          }
          img {
            // width: ;
            height: 12px;
            margin-top: 4px;
          }
            ::v-deep .el-textarea__inner {
              font-size: 10px;
          }
        }

    }
  }
}

.upload_content {
  height: 40%;
  width: 100%;
  display: flex;
  justify-content: space-between;
  background: #fafafa;
  padding: 20px;
}

.upload_left {
  width: 30%;
  .upload-area {
    height: 100%;
    padding: 20px;
    text-align: center;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: center;
    &:hover {
      border-color: #409EFF;
    }

    .upload-tip {
      margin-top: 10px;
      font-size: 12px;
      color: #666;
      line-height: 1.5;
    }
  }
}

.upload_right {
  width: 68%;
  height: 100%;
  .file-list {
    border-radius: 6px;
    height: 100%;
    overflow-y: auto;
    @include scrollBar;
    .file-item {
      display: flex;
      align-items: center;
      padding: 10px;
      border-bottom: 1px solid #eee;
      &:last-child {
        border-bottom: none;
      }
      .file-name {
        flex: 1;
        font-size: 14px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .file-size {
        margin: 0 20px;
        color: #999;
        white-space: nowrap;
      }
      .progress {
        width: 150px;
        margin: 0 20px;
      }
      .operations {
        white-space: nowrap;
        span {
          margin-left: 10px;
          color: #409EFF;
          cursor: pointer;
          &.delete {
            color: #F56C6C;
          }
        }
      }
    }
  }
}

.upload_title {
  padding: 20px 20px 0;
}
</style>
