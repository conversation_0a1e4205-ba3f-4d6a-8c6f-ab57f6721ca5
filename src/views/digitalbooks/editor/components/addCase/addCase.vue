<template>
  <div v-if="dialogShow" class="pop-body" v-loading="loading">
    <div class="pop-main">
      <div class="pop-title">
        交互案例
        <i class="el-icon-close icon-close" @click="close"></i>
      </div>
      <div class="pop-content">
        <el-form ref='Form' :model='formData' :rules='rules' label-width='18%' label-position='left'>
          <el-form-item label="案例类型" prop="caseType">
            <el-select v-model='formData.caseType' value-key='id' placeholder='请选择案例类型' style='width: 100%;' @change="valueChange">
              <el-option
                v-for='item in caseList'
                :key='item.id'
                :label='item.title'
                :value='item'>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="案例标题" prop="caseName">
            <el-input v-model="formData.caseName" maxlength="20" show-word-limit placeholder="请输入案例标题"/>
          </el-form-item>
          <el-form-item label="案例简介">
            <el-input v-model="formData.caseDescription" maxlength="200" show-word-limit type='textarea' :rows='5' placeholder="请输入案例简介"/>
          </el-form-item>
          <el-form-item label="案例封面">
            <el-upload
              class="avatar-uploader"
              action=""
              :show-file-list="false"
              accept=".png, .jpg, .jpeg"
              :before-upload="beforeAvatarUpload"
            >
              <img v-if="formData.cover" :src="ossUrl + formData.cover" :key='imageKey' class="avatar" />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              <div slot="tip" class='upload-tip'>
                建议上传16:9的图片，尺寸320*180，支持jpg/png格式
              </div>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
      <div class="pop-bottom">
        <el-button type='primary' size='mini' class='bottom-btn' @click="handleConfirm">提交</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { getFileUploadAuthor } from '@/api/user-api'
import axios from 'axios'
import { addSetImgSizeModal, closeSetImgSize, setData } from '@/views/digitalbooks/editor/components/setImgSize'
import { getTrainingCaseTemplateList, training } from '@/api/training-api'
import store from '@/store'
import router from '@/router'

export default {
  name: 'AddCase',
  data() {
    return {
      dialogShow: false,
      formData: {
        caseType: {},
        caseName: '',
        caseDescription: '',
        cover: ''
      },
      ossUrl: '',
      imageKey: 0,
      rules: {
        caseType: [
          { required: true, message: '请选择案例类型', trigger: 'change' }
        ],
        caseName: [
          { required: true, message: '请输入案例标题', trigger: 'blur' },
          { max: 20, message: '标题不能超过20个字符', trigger: 'blur' }
        ]
      },
      loading: false,
      caseList: [],
      token: ''
    }
  },
  mounted() {
    this.token = `Bearer ${router.currentRoute.query.token}`
  },
  methods: {
    async getCaseList() {
      try {
        this.loading = true
        const { data } = await getTrainingCaseTemplateList()
        this.caseList = data || []
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    },
    valueChange(item) {
      if (item) {
        this.formData.cover = item.cover || ''
        this.formData.caseName = item.title
        this.formData.caseDescription = item.subtitle || ''
      }
    },
    initData() {
      this.formData = {
        caseType: {},
        caseName: '',
        caseDescription: '',
        cover: ''
      }
      this.ossUrl = ''
      this.imageKey = 0
      this.loading = false
    },
    setData(param) {
      document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '9999'
      this.dialogShow = true
      this.formData = {
        caseType: param.caseType,
        caseName: param.title || '',
        caseDescription: param.subtitle || '',
        cover: param.data.cover || ''
      }
      this.ossUrl = ''
      this.formData.trainingId = param.id || ''
    },
    open(cbs = {}) {
      this.$nextTick(() => {
        this.dialogShow = true
      })
      this.cbs = cbs
      document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '1'
      this.getCaseList()
    },
    onSubmit(data) {
      if (Object.prototype.toString.call(this.cbs['onSubmit']) === '[object Function]') {
        this.cbs['onSubmit'](data)
      }
    },
    close() {
      this.dialogShow = false
      this.$nextTick(() => {
        this.initData()
      })
      document.getElementsByClassName('tox-tinymce-aux')[0].style.zIndex = '9999'
    },
    async beforeAvatarUpload(file) {
      try {
        this.loading = true
        const isLt1M = file.size / 1024 < 500
        if (!isLt1M) {
          this.$message.warning('上传图片超过500Kb,可能会影响体验，请压缩图片')
        }
        const { data } = await getFileUploadAuthor({
          mediaType: 'IMAGE',
          contentType: '',
          quantity: 1,
          fileName: file.name
        })
        this.ossUrl = data[0].ossConfig.host
        this.progress = true
        const formData = new FormData()
        formData.append('success_action_status', '200')
        formData.append('callback', '')
        formData.append('key', data[0].fileName)
        formData.append('policy', data[0].policy)
        formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
        formData.append('signature', data[0].signature)
        formData.append('file', file)
        const _this = this
        await axios.post(this.ossUrl, formData)
        if (!isLt1M) {
          setData({
            url: `${this.ossUrl}/${data[0].fileName}`
          })
          addSetImgSizeModal({
            onSubmit (data) {
              _this.ossUrl = ''
              _this.formData.cover = `${data.url}`
              closeSetImgSize()
            },
            onCancel() {
              _this.ossUrl = ''
            }
          })
        } else {
          this.formData.cover = `/${data[0].fileName}`
        }
        console.log(`${this.ossUrl}/${data[0].fileName}`)
        this.imageKey++
        return false
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    },
    async handleConfirm() {
      this.$refs.Form.validate(async (valid) => {
        if (valid) {
          try {
            this.loading = true
            const { data } = await training({
              linkSourceId: store.state.app.activeCatalogueId,
              trainingLinkType: 'DIGITAL_BOOK_CATALAGUE_CONTENT_CASE',
              trainingType: 'CASE_PRACTICE',
              trainingName: this.formData.caseName,
              subtitle: this.formData.caseDescription,
              cover: this.formData.cover,
              practiceUrl: this.formData.caseType.practiceUrl || '',
              trainingId: this.formData.trainingId || null
            },{ authorization: this.token })
            this.onSubmit({ title: data.trainingName, subtitle: data.subtitle, id: data.trainingId, caseType: this.formData.caseType, data })
            this.initData()
            this.dialogShow = false
          } catch (e) {
            console.log(e)
          } finally {
            this.loading = false
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.pop-body{
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  ::v-deep .el-form-item__label{
    text-align: right;
  }
  .pop-main{
    width: 50%;
    max-height: 90vh;
    //height: 50%;
    background-color: white;
    border-radius: 10px;
    padding-bottom: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    .pop-title{
      width: 100%;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      border-bottom: 1px solid #e0e0e0;
      font-size: 16px;
      .icon-close{
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 16px;
        cursor: pointer;
        &:hover{
          color: #409EFF;
        }
      }
    }
    .pop-content{
      flex-grow: 1;
      width: 100%;
      padding: 20px;
      overflow-y: auto;
      .upload-tip{
        width: 100%;
        height: 20px;
        font-size: 10px;
        color: #8c939d;
        margin-top: -20px;
        display: flex;
        padding-top: 5px;
      }
    }
    .pop-bottom{
      width: 100%;
      height: 30px;
      margin-top: 5px;
      display: flex;
      justify-content: center;
      align-items: center;
      .bottom-btn{
        width: 200px;
      }
    }
  }
}
::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 320px;
  height: 180px;
  line-height: 180px;
  text-align: center;
}
.avatar {
  width: 320px;
  height: 180px;
  display: block;
  object-fit: cover;
}
</style>
