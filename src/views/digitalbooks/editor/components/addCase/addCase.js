import Vue from 'vue'
import addCase from './addCase.vue'

let $vm, dialogConstructor

function initInstance() {
  if (!dialogConstructor) {
    dialogConstructor = Vue.extend(addCase)
  }
  if (!$vm) {
    // eslint-disable-next-line new-cap
    $vm = new dialogConstructor()
    $vm.$mount() // 挂载一次
    document.body.appendChild($vm.$el) // 将组件添加到 DOM
  }
}

export function addCaseModal(cbs) {
  initInstance()
  $vm.open(cbs) // 调用 open 方法而无需重复挂载
}

export function closeCase() {
  if ($vm) {
    $vm.close()
    // 销毁实例以释放内存
    $vm.$destroy()
    document.body.removeChild($vm.$el)
    $vm = null // 重置 $vm
  }
}

export function setData(params) {
  initInstance() // 确保 $vm 已初始化
  $vm.setData(params) // 设置数据
}
