<template>
  <div class="course-package">
    <div class="wrap">
      <div class="header">
        <img src="@/assets/images/skyclass/arrow-back.png" alt="返回按钮" @click="backToHome" />
        <div class="back" @click="backToHome">返回</div>
      </div>
      <template v-if="courseInfo">
        <!-- 顶部操作 -->
        <div class="container operation">
          <img :src="courseInfo.coverUrl || DefaultCover" alt="" />
          <div class="right">
            <div class="name mb-20">{{ +type === 0 ? courseInfo.name || '' : courseInfo.title || '' }}</div>
            <div class="line mb-20"></div>
            <div class="content mb-15">{{ +type === 0 ? courseInfo.subtitle || '' : courseInfo.subTitle || '' }}</div>
            <div class="flex items-center info">
              <div class="mr-15">课时：{{ +type === 0 ? courseInfo.number : courseInfo.len }}课时</div>
              <div class="three-line-box mr-15">
                <div v-for="item in 3" :key="item" class="three-line"></div>
              </div>
              <div class="mr-15">适用年级：{{ level || '/' }}</div>
              <div class="three-line-box mr-15">
                <div v-for="item in 3" :key="item" class="three-line"></div>
              </div>
              <div class="mr-15">课程形式：{{ +type === 0 ? '直播课' : '双师AI课堂' }}</div>
            </div>
            <!-- <div class="classpro-btn">添加到我的课程</div> -->
          </div>
        </div>
        <!-- 教研团队 -->
        <div v-show="false" class="container">
          <div class="pacakge-title">
            <div class="line"></div>
            <span>教研团队</span>
          </div>
          <div class="expert-list swiper-no-swiping">
            <swiper
              ref="Swiper"
              class="swiper"
              :options="swiperOption"
              @slideChange="slideChange"
            >
              <swiper-slide v-for="index in 11" :key="index">
                <div class="flex items-center justify-center">
                  <img class="mr-10" :src="DefaultAvatar" alt="" />
                  <div class="expert">
                    <div class="expert-name">万分</div>
                    <el-tooltip content="安徽省寿县朱家集楚王安徽省寿县朱家集楚王">
                      <div class="expert-intro">安徽省寿县朱家集楚王安徽省寿县朱家集楚王</div>
                    </el-tooltip>
                  </div>
                </div>
              </swiper-slide>
              <div slot="pagination" class="swiper-pagination"></div>
            </swiper>
            <img :src="swiperPrevShow ? arrow : arrowGrey" class="swiper-prev" @click="slidePrev" />
            <img :src="swiperNextShow ? arrow : arrowGrey" class="swiper-next" @click="slideNext" />
          </div>
        </div>
        <!-- 课程列表 -->
        <div class="container">
          <div class="pacakge-title">
            <div class="line"></div>
            <span>课程大纲</span>
          </div>
          <div v-if="lessonList.length > 0" class="course-list">
            <div v-for="(item, index) in lessonList" :key="item.id" class="course-item">
              <div class="flex items-center mb-20">
                <img class="mr-20" :src="item.coverUrl || DefaultCover" alt="" />
                <div class="course-info">
                  <div class="course-name mb-20">{{ +type === 0 ? item.name || '' : item.title || '' }}</div>
                  <div class="course-intro">{{ +type === 0 ? item.subtitle || '' : item.subTitle || '' }}</div>
                </div>
                <template v-if="+type === 1">
                  <template v-if="hasCourse">
                    <div class="flex flex-col">
                      <div class="classpro-btn mb-5" @click="toInClass(item, index)">上课</div>
                      <div class="classpro-btn" @click="toDetail(item, index)">备课</div>
                    </div>
                  </template>
                  <template v-else>
                    <div v-if="index === 0" class="flex flex-col">
                      <div class="classpro-btn mb-5" @click="toInClass(item, index)">上课</div>
                      <div class="classpro-btn" @click="toDetail(item, index)">备课</div>
                    </div>
                    <div v-else class="classpro-btn" @click="applyShow = true">申请使用</div>
                  </template>
                </template>
              </div>
              <div class="line mb-20"></div>
            </div>
          </div>
          <div v-else class="empty">
            <img src="@/assets/images/empty.png" alt="empty" />
            <div class="hint hint-padding">我们正在努力生产课程，敬请期待～</div>
          </div>
        </div>
      </template>
    </div>
    <apply v-if="applyShow" :course-id="packageId" :show="applyShow" @close="applyShow = false" />
  </div>
</template>

<script>
import apply from '@/components/classPro/ClassBind/apply.vue'
import DefaultCover from '@/assets/images/default-cover.jpg'
import DefaultAvatar from '@/assets/images/profile.png'
import arrow from '@/assets/images/course/arrow.svg'
import arrowGrey from '@/assets/images/course/arrow-grey.svg'
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import 'swiper/css/swiper.css'
import { getCourse, getCourseLessonPlanList, getAiCourse, getAiCourseUnitList } from '@/api/course-api'
import { getAiConfig } from '@/api/dictionary-api.js'
import { throttle } from '@/utils/index'
import { getToken } from '@/utils/auth.js'

export default {
  components: {
    apply,
    Swiper,
    SwiperSlide
  },
  data () {
    return {
      DefaultCover,
      DefaultAvatar,
      arrow,
      arrowGrey,
      allowSlideNext: false,
      applyShow: false,
      swiperOption: {
        slidesPerView: 5,
        spaceBetween: 0,
        slidesPerGroup: 5,
        loop: true,
        loopFillGroupWithBlank: true,
        noSwiping: true,
        pagination: {
          el: '.swiper-pagination',
          clickable: true
        }
        // navigation: {
        //   nextEl: '.swiper-next',
        //   prevEl: '.swiper-prev'
        // }
      },
      categoryId: this.$route.params.categoryId,
      packageId: this.$route.params.packageId,
      type: this.$route.params.type,
      courseInfo: null,
      swiperPrevShow: true,
      swiperNextShow: true,
      lessonList: [],
      aiSite: undefined,
      hasCourse: true
    }
  },
  computed: {
    level () {
      if (this.courseInfo) {
        if (+this.type === 0) {
          if (this.courseInfo.difficultyLevel === -1) {
            return '所有年级'
          }
          if (this.courseInfo.difficultyLevel === 0) {
            return '学前班'
          }
          return `${this.courseInfo.difficultyLevel}年级`
        } else {
          return this.courseInfo.level || ''
        }
      }
      return ''
    }
  },
  created () {
    if (+this.$route.query.h === 0) {
      // 判断是否试用
      this.hasCourse = false
    }
    if (+this.type === 0) {
      this._getCourse()
      this._getCourseLessonPlanList()
    } else {
      this._getAiConfig()
      this._getAiCourse()
      this._getAiCourseUnitList()
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.slideChange()
    })
  },
  methods: {
    backToHome () {
      if (this.$route.query && this.$route.query.f === 'recom') {
        this.$router.push('/classpro')
      } else {
        if (+this.categoryId === 0) {
          this.$router.push('/classpro/skyCourse')
        } else {
          this.$router.push(`/classpro/course/detail/${this.categoryId}`)
        }
      }
    },
    slideChange () {
      var realIndex = this.$refs.Swiper && this.$refs.Swiper.$swiper && this.$refs.Swiper.$swiper.realIndex
      this.swiperPrevShow = realIndex !== 0
      this.swiperNextShow = realIndex + 5 < 11
    },
    slidePrev () {
      if (!this.swiperPrevShow) return
      this.$refs.Swiper.$swiper.slidePrev()
    },
    slideNext () {
      if (!this.swiperNextShow) return
      this.$refs.Swiper.$swiper.slideNext()
    },
    //  获取课程详情
    _getCourse () {
      const params = {
        'courseId': this.packageId
      }
      getCourse(params).then(
        response => {
          this.courseInfo = response.data
        }
      )
    },
    //  获取课程包大纲信息-直播课
    _getCourseLessonPlanList () {
      const params = { 'courseId': this.packageId }
      getCourseLessonPlanList(params, false).then(response => {
        this.lessonList = response.data
      })
    },
    //  获取AI课详情
    _getAiCourse () {
      const params = {
        'aicourseId': this.packageId
      }
      getAiCourse(params).then(
        response => {
          if (response.data && response.data.length > 0) {
            this.courseInfo = response.data[0]
            if (this.courseInfo.studentsCourse) {
              this.hasCourse = true
            }
          }
        }
      )
    },
    //  获取课程包大纲信息-直播课
    _getAiCourseUnitList () {
      const params = {
        'aicourseId': this.packageId
      }
      getAiCourseUnitList(params).then(response => {
        this.lessonList = response.data
      })
    },
    // 获取ai课地址
    async _getAiConfig () {
      getAiConfig({ configType: 'AI_PROJECT_DNS' })
        .then(response => {
          if (response != null) {
            this.aiSite = response.data[0].keyValue || null
            if (this.aiSite.substring(this.aiSite.length - 1, this.aiSite.length) === '/') {
              this.aiSite = this.aiSite.substring(0, this.aiSite.length - 1)
            }
            console.log(this.aiSite)
          }
        })
    },
    // 去AI课首页
    toInClass (item, index) {
      if (this.courseInfo.type !== 'SELF' && !item.externalPrepareUrl) {
        this.$message.error('课程开通中')
        return
      }
      if (item.externalPrepareUrl) {
        // 三方ai课
        window.open(item.externalPrepareUrl, '_blank')
      } else {
        const aiSite = this.aiSite
        const _selt = this
        // const id = this.$store.getters.id
        if (this.hasCourse) {
          // 已开通用户去到AI课首页
          throttle(function () {
            const token = getToken()
            window.open(aiSite + `/ai/${item.aiCourseId}/${_selt.courseInfo.studentsCourse.id}/${item.id}?close=1&&token=${token}`, '_blank')
          }, 3000)
        } else {
        // 未开通用户试用上课页
          window.open(aiSite + `/ai/${item.aiCourseId}/0/${item.id}?close=1`, '_blank')
        //   window.open(`http://localhost:8080` + `/ai/${item.aiCourseId}/0/${item.id}?close=1`, '_blank')
        }
      }
    },
    //  查看详情
    toDetail (item, index) {
      if (this.courseInfo.type !== 'SELF' && !item.externalPrepareUrl) {
        this.$message.error('课程开通中')
        return
      }
      if (item.externalPrepareUrl) {
        window.open(item.externalPrepareUrl, '_blank')
      } else {
        const aiSite = this.aiSite
        const id = this.$store.getters.id
        if (this.hasCourse) {
          // 已开通用户去到AI课备课
          throttle(function () {
            const token = getToken()
            window.open(aiSite + `/aiPrepare/${item.aiCourseId}/0/${index}/${item.id}/${id}/?close=1&&token=${token}`, '_blank')
          }, 3000)
        } else {
          // 未开通用户试用上课页
          window.open(aiSite + `/aiPrepare/${item.aiCourseId}/0/${index}/${item.id}/${id}?close=1`, '_blank')
        }
      }
    }
  }
}
</script>

<style lang='scss' scoped>

.course-package {
    // height: calc(100% - 62px);
    height: calc(100% - 1px);
    padding: 10px;
    width: 100%;
    overflow: scroll;
    overflow-x: hidden;
    @include scrollBar;

  .wrap {
    .container {
        width: 100%;
        padding: 20px 30px;
        background: #FFFFFF;
        box-shadow: 0 3px 14px 0 rgba(233,240,255,0.50);
        border-radius: 10px;
        margin-bottom: 10px;
        position: relative;
    }

    .pacakge-title {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        .line {
            width: 3px;
            height: 16px;
            background: #3479FF;
            border-radius: 3.5px;
            margin-right: 8px;
        }

        span {
            font-family: PingFangSC-Medium;
            font-weight: 500;
            font-size: var(--font-size-L);
            color: #1C1B1A;
            letter-spacing: 0.26px;
        }

        .tip {
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: var(--font-size-M);
            color: #1C1B1A;
            letter-spacing: 0.19px;
            margin-left: auto;
            margin-right: 5px;
            line-height: 17px;
            cursor: pointer;
        }

        i {
            transform: rotate(180deg);
            font-size: var(--font-size-M);
            margin-bottom: 2px;
            cursor: pointer;
        }
    }

    .header {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        img {
            width: 13px;
            height: 11px;
            cursor: pointer;
            object-fit: contain;
        }

        .back {
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: var(--font-size-L);
            color: #1C1B1A;
            letter-spacing: 0.22px;
            margin: 0 20px 0 8px;
            cursor: pointer;
        }

        span {
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 10px;
            color: #999999;
            letter-spacing: 0.16px;
        }
    }

    .operation {
        display: flex;
        position: relative;

        img {
            height: 134px;
            width: 240px;
            object-fit: cover;
            border-radius: 10px;
        }

        .right {
            min-height: 154px;
            display: flex;
            flex-direction: column;
            //padding: 0 20px;
          margin-left: 20px;
        }

        .name {
            font-family: PingFangSC-Medium;
            font-weight: 500;
            font-size: var(--font-size-L);
            color: #1C1B1A;
            letter-spacing: 0.26px;
            margin-right: 15px;
            line-height: 22px;
        }

        .line {
            width: 20px;
            height: 3px;
            background: #3479FF;
            border-radius: 1.5px;
        }

        .classpro-btn {
          position: absolute;
          right: 20px;
          top: 30px;
          width: 113px;
          height: 35px;
          border-radius: 5px;
        }

        .info {
          margin-top: auto;
          line-height: 17px;
          font-weight: 400;
          font-size: var(--font-size-L);
          color: #4E8AFF;
          letter-spacing: 0;
        }

        .three-line-box {
          height: 10px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
        }

        .three-line {
          width: 1px;
          height: 3px;
          background: #CBCBCB;
        }
    }

    .content {
        font-weight: 400;
        font-size: var(--font-size-L);
        color: #1C1B1A;
        letter-spacing: 0.22px;
        line-height: 20px;
        // white-space: pre-wrap;
        word-break: break-all;
    }

    .expert-list {
      position: relative;
    }

    .swiper {
        height: 98px;
        margin: 0 70px;

        img {
            width: 60px;
            height: 60px;
            object-fit: cover;
        }

        .expert-name {
          line-height: 20px;
          font-weight: 500;
          font-size: var(--font-size-L);
          color: #1C1B1A;
          letter-spacing: 0;
          margin-bottom: 3px;
        }

        .expert-intro {
          line-height: 17px;
          font-weight: 400;
          font-size: var(--font-size-L);
          color: #2E2D2C;
          letter-spacing: 0;
          width: 135px;
          cursor: pointer;
          @include ellipses(1);
        }
    }

    .swiper-prev-disable,
    .swiper-prev {
      position: absolute;
      object-fit: contain;
      width: 30px;
      height: 30px;
      top: 15px;
      left: 0;
      z-index: 9;
      cursor: pointer;
    }

    .swiper-next {
      position: absolute;
      object-fit: contain;
      width: 30px;
      height: 30px;
      top: 15px;
      right: 0;
      z-index: 9;
      transform: scaleX(-1);
      cursor: pointer;
    }

    .course-item {
      width: 100%;
      font-weight: 400;

      img {
        width: 180px;
        height: 102px;
        object-fit: cover;
        border-radius: 5px;
      }

      .course-info {
        height: 102px;
        flex: 1
      }

      .course-name {
        line-height: 22px;
        font-weight: 500;
        font-size: var(--font-size-L);
        color: #1C1B1A;
        letter-spacing: 0;
        @include ellipses(1);
      }

      .course-intro {
        font-size: var(--font-size-L);
        color: #1C1B1A;
        line-height: 20px;
        max-width: 750px;
        white-space: pre-wrap;
        @include ellipses(3)
      }

      .classpro-btn {
        width: 70px;
        height: 25px;
        font-size: var(--font-size-L);
        border-radius: 5px;
        margin-left: 10px;
      }

      .line {
        width: 100%;
        height: 1px;
        background:#EAF0FF;
      }
    }

    .empty {
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        img {
            width: 126px;
            height: 128px;
        }

        .hint {
            display: flex;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: var(--font-size-L);
            color: #8C8C8C;
            letter-spacing: 0.22px;

            .hint-blue {
                color: rgba(31, 102, 255, 1);
                cursor: pointer;
            }
        }

        .hint-padding {
            padding:15px 0 4px
        }
    }

    .mr-10 {
      margin-right: 10px;
    }

    .mr-15 {
      margin-right: 15px;
    }

    .mr-20 {
      margin-right: 20px;
    }

    .mb-5 {
      margin-bottom: 5px;
    }

    .mb-15 {
      margin-bottom: 15px;
    }
    .mb-20 {
      margin-bottom: 20px;
    }
  }
}
</style>
