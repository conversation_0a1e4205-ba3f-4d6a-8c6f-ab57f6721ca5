<template>
  <div v-if="dialogShow" class="feedback-body">
    <div class="feedback-main" v-loading='loading' element-loading-background="rgba(255, 255, 255, 0.3)">
      <i class="el-icon-close feedback-close" @click='close'></i>
      <div class="feedback-title">用户反馈</div>
      <div class="feedback-subtitle">感谢您的反馈，您的意见对我们至关重要，将帮助我们不断改进服务。</div>
      <div class="feedback-content">
        <el-form ref='form' :model='formData' :rules="rules" label-position='top'>
          <el-form-item label="反馈内容" prop="content">
            <el-input
              type="textarea"
              v-model="formData.content"
              :rows="3"
              placeholder="请详细描述您的反馈内容"
              maxlength="400"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="上传图片（最多9张）">
            <el-upload
              v-if="imageList.length === 0"
              class="uploader"
              action=""
              drag
              mutiple
              accept='image/*'
              :show-file-list="false"
              :before-upload="beforeUpload">
              <div class='el-upload__title'>点击或拖拽上传图片</div>
              <div class='el-upload__tip'>支持 JPG、PNG 格式，每张图片不超过 5MB</div>
            </el-upload>
            <div class="fileList-view" v-else>
              <div class="file-item" v-for="(item, index) in imageList" :key="index">
                <img :src="item.ossUrl + item.url" />
                <img src="@/assets/digitalbooks/read/red-close.png" class='file-item-cancel' @click="imageList.splice(index, 1)"/>
              </div>
              <el-upload
                class="small-uploader"
                action=""
                :show-file-list="false"
                accept='image/*'
                :before-upload="beforeUpload">
                <i class="el-icon-plus"></i>
                <div class='small-upload-tip'>支持 JPG、PNG 格式，每张图片不超过 5MB</div>
              </el-upload>
            </div>
          </el-form-item>
          <el-row :gutter='10'>
            <el-col :span='8'>
              <el-form-item label="身份" prop="userType">
                <el-select v-model='formData.userType' placeholder="请选择您的身份" style='width: 100%'>
                  <el-option
                    v-for='item in userTypeList'
                    :key='item.value'
                    :label='item.label'
                    :value='item.value' />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="学校名称">
                <el-input v-model='formData.schoolName' placeholder='请输入您的学校名称' />
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="职务">
                <el-input v-model='formData.position' placeholder='请输入您的职务' />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter='10'>
            <el-col :span='8'>
              <el-form-item label="学科专业">
                <el-input v-model='formData.subjectMajor' placeholder='请输入您的学科专业' />
              </el-form-item>
            </el-col>
            <el-col :span='8'>
              <el-form-item label="邮箱">
                <el-input v-model='formData.email' placeholder='请输入您的邮箱' />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="feedback-bottom">
          <el-button type='primary' size='small' @click="submitAction">
            <i class="el-icon-s-promotion feedback-submit"></i>
            提交反馈
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { feedback } from '@/api/system-api'
import { getFileUploadAuthor } from '@/api/user-api'
import axios from 'axios'

export default {
  name: 'Feedback',
  data () {
    return {
      dialogShow: false,
      loading: false,
      imageList: [],
      rules: {
        content: [
          { required: true, message: '请输入反馈内容', trigger: 'blur' },
          { max: 400, message: '反馈内容长度不超过400字', trigger: 'blur' }
        ],
        userType: [
          { required: true, message: '请选择您的身份', trigger: 'change' }
        ]
      },
      formData: {
        content: '',
        userType: '',
        schoolName: '',
        position: '',
        subjectMajor: '',
        email: ''
      },
      userTypeList: [
        { label: '学生', value: 'STUDENT' },
        { label: '教师', value: 'TEACHER' },
        { label: '其他', value: 'OTHER' }
      ]
    }
  },
  methods: {
    open () {
      this.dialogShow = true
    },
    close () {
      this.initData()
      this.dialogShow = false
    },
    initData() {
      this.formData = {
        content: '',
        userType: '',
        schoolName: '',
        position: '',
        subjectMajor: '',
        email: ''
      }
      this.imageList = []
    },
    async beforeUpload(file) {
      if (file.type.indexOf('image/') === -1) {
        this.$message.error('只能上传图片格式')
        return
      }
      if (this.imageList.length >= 9) {
        this.$message.error('最多只能上传9张图片')
        return false
      }
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isLt5M) {
        this.$message.error('上传图片大小不能超过 5MB!')
        return false
      }
      try {
        this.loading = true
        const { data } = await getFileUploadAuthor({
          mediaType: 'IMAGE',
          contentType: '',
          quantity: 1,
          fileName: file.name
        })
        const formData = new FormData()
        formData.append('success_action_status', '200')
        formData.append('callback', '')
        formData.append('key', data[0].fileName)
        formData.append('policy', data[0].policy)
        formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
        formData.append('signature', data[0].signature)
        formData.append('file', file)
        await axios.post(data[0].ossConfig.host, formData)
        this.imageList.push({
          url: `/${data[0].fileName}`,
          ossUrl: data[0].ossConfig.host
        })
      } catch (e) {
        console.log('上传图片失败')
        this.$message.error('上传图片失败')
      } finally {
        this.loading = false
      }
    },
    async submitAction() {
      try {
        this.loading = true
        await this.$refs.form.validate()
        const params = {
          content: this.formData.content,
          attachMent: this.imageList.reduce((pre, cur) => {
            pre.push(cur.url)
            return pre
          }, []).join(','),
          feedbackType: 'advisement',
          feedbackSence: 'BINGO_CLASS',
          userInfo: JSON.stringify({
            userType: this.formData.userType,
            schoolName: this.formData.schoolName,
            position: this.formData.position,
            subjectMajor: this.formData.subjectMajor,
            email: this.formData.email
          })
        }
        await feedback(params)
        this.$message.success('反馈提交成功，谢谢您的意见！')
        this.close()
      } catch (error) {
        console.error('提交失败:', error)
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped lang='scss'>
.feedback-body{
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  .feedback-main{
    width: 70%;
    max-height: 98vh;
    background: rgba(240, 245, 255, 1);
    border-radius: 10px;
    padding: 10px 35px 20px 35px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    position: relative;
    .feedback-close{
      position: absolute;
      right: 10px;
      top: 10px;
      font-size: 20px;
      cursor: pointer;
    }
    .feedback-title{
      width: 100%;
      height: 20px;
      flex-shrink: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      font-weight: 500;
      color: rgba(47, 128, 237, 1);
    }
    .feedback-subtitle{
      width: 100%;
      height: 15px;
      margin-top: 5px;
      flex-shrink: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: rgba(108, 117, 125, 1);
    }
    .feedback-content{
      margin-top: 5px;
      width: 100%;
      background-color: white;
      border-radius: 10px;
      border: 1px solid rgba(224, 230, 237, 1);
      box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.08);
      padding: 10px 30px 20px 30px;
      ::v-deep .el-form-item__label{
        font-size: 14px;
        padding-bottom: 0 !important;
        height: 30px;
      }
      ::v-deep .el-form-item{
        margin-bottom: 5px !important;
      }
      ::v-deep .el-upload{
        width: 100%;
        height: 100px;
        cursor: pointer;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
      ::v-deep .el-upload-dragger{
        width: 100%;
        height: 100%;
        border: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
      .fileList-view{
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        .small-uploader{
          width: 120px;
          height: 100px;
          flex-shrink: 0;
          border: 1px dashed #d9d9d9;
          border-radius: 6px;
          cursor: pointer;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 0 15px;
          .small-upload-tip{
            height: 10px;
            line-height: 10px;
            white-space: pre-wrap;
            font-size: 8px;
            margin-top: 0;
            color: rgba(108, 117, 125, 1);
          }
        }
        .file-item{
          flex-shrink: 0;
          width: 120px;
          height: 100px;
          border-radius: 6px;
          overflow: hidden;
          position: relative;
          border: 1px solid #d9d9d9;
          img{
            width: 100%;
            height: 100px;
            object-fit: contain;
          }
          .file-item-cancel{
            width: 15px;
            height: 15px;
            position: absolute;
            top: 3px;
            right: 3px;
            cursor: pointer;
          }
        }
      }
      .uploader{
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100px;
        .el-upload__title{
          height: 30px;
          line-height: 30px;
          font-size: 14px;
        }
        .el-upload__tip{
          height: 20px;
          line-height: 20px;
          font-size: 10px;
          margin-top: 0;
          color: rgba(108, 117, 125, 1);
        }
      }
    }
    .feedback-bottom{
      width: 100%;
      flex-shrink: 1;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 15px;
      font-size: 18px;
      .feedback-submit{
        margin-right: 5px;
        color: white;
      }
    }
  }
}
</style>
