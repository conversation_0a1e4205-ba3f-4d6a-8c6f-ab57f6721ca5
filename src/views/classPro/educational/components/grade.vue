<template>
  <div class="grade-wrap">
    <div class="grade-title">
      <template v-if="school">
        <div>{{ school.name }}</div>
        <div style="color: #828282;">已激活</div>
      </template>
      <template v-else>
        <div>暂未激活账号</div>
        <div class="btn-active" @click="bindSchoolShow = true">激活账号</div>
      </template>
    </div>
    <div class="table-box">
      <div class="table-title">
        <div v-if="fromType === 'classSetting'" class="flex items-center">
          <!-- <div class="icon-i"></div> -->
          <div>我的班级</div>
        </div>
        <div v-else class="flex items-center">
          <div class="icon-i"></div>
          <div>班级列表</div>
        </div>
        <div>
          <div class="btn-active" @click="addClassShow = true">
            创建班级
          </div>
        </div>
      </div>
      <el-table
        :data="tableData"
        :header-row-style="{ background: '#F8FAFF' }"
        :header-cell-style="{
          background: '#F8FAFF',
          color: '#3479FF',
          border: 'none',
          // font-weight: '400'
        }"
        :header-row-class-name="tableHeader"
        :row-class-name="rowClass"
        :cell-class-name="cellClass"
        :height="'calc(100% - 70px)'"
        style="width: 100% "
      >
        <el-table-column prop="name" align="left" label="班级名称">
          <template slot-scope="scope">
            <div class="flex items-center" @click="creatQrCode(scope.row)">
              <div>{{ scope.row.name }}</div>
              <div>
                <img style="vertical-align: middle;" src="../../../../assets/parent/assistant-code.svg" />
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column v-if="fromType === 'classSetting'" prop="type" align="left" label="班级类型">
          <template slot-scope="scope">
            {{ scope.row.type | classType }}
          </template>
        </el-table-column>
        <el-table-column v-if="fromType === 'classSetting'" prop="assistantList" align="left" label="管理教师">
          <template slot-scope="scope">
            {{ scope.row.assistantList | techName }}
          </template>
        </el-table-column>

        <el-table-column align="left" label="学生名单">
          <template slot-scope="scope">
            <div class="pointer" @click="showStuLists(scope.row.userExtra)">{{ scope.row.userExtra | stuList }}</div>
            <el-button class="item-handle" type="text" @click="inputStu(scope.row)">导入学生</el-button>
          </template>
        </el-table-column>

        <el-table-column v-if="fromType === 'classSetting'" align="left" label="班级介绍">
          <template slot-scope="scope">
            <el-button class="item-handle" type="text" @click="changeClassInfo(scope.row)">介绍</el-button>
          </template>
        </el-table-column>

        <el-table-column v-if="fromType === 'classSetting'" prop="createdAt" align="left" label="创建时间">
          <template slot-scope="scope">
            <span>{{ scope.row.createdAt | formTime }}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="fromType !== 'classSetting'" prop="courseList" align="left" label="在学课程">
          <template slot-scope="scope">
            <el-popover placement="bottom-start">
              <div class="pop-box">
                <div v-for="item in scope.row.courseList" :key="item.id" class="item-scope">
                  {{
                    (item.course && item.course.name) ||
                      (item.aicourse && item.aicourse.title)
                  }}
                </div>
              </div>
              <div slot="reference">
                <div v-if="scope.row.courseList && scope.row.courseList[0]" class="item-scope">
                  {{
                    (scope.row.courseList[0].course &&
                      scope.row.courseList[0].course.name) ||
                      (scope.row.courseList[0].aicourse &&
                        scope.row.courseList[0].aicourse.title)
                  }}
                </div>
                <div v-if="scope.row.courseList && scope.row.courseList[1]" class="item-scope">
                  {{
                    (scope.row.courseList[1].course &&
                      scope.row.courseList[1].course.name) ||
                      (scope.row.courseList[1].aicourse &&
                        scope.row.courseList[1].aicourse.title)
                  }}
                </div>
                <div
                  v-show="scope.row.courseList && scope.row.courseList.length > 2
                  "
                  class="tl"
                >
                  ...
                </div>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column v-if="fromType !== 'classSetting'" prop="lessonModel" align="left" label="课程形式">
          <template slot-scope="scope">
            <el-popover placement="bottom-start">
              <div class="pop-box">
                <div v-for="item in scope.row.lessonModel" :key="item" class="item-scope">
                  {{ item }}
                </div>
              </div>
              <div slot="reference">
                <div v-if="scope.row.lessonModel[0]" class="item-scope">
                  {{ scope.row.lessonModel[0] }}
                </div>
                <div v-if="scope.row.lessonModel[1]" class="item-scope">
                  {{ scope.row.lessonModel[1] }}
                </div>
                <div v-show="scope.row.lessonModel.length > 2" class="tl">
                  ...
                </div>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="" align="left" label="操作">
          <template slot-scope="scope">
            <span v-if="fromType !== 'classSetting'" class="item-handle" @click="gotoDetail(scope.row)">详情</span>
            <span v-if="fromType !== 'classSetting'" class="item-handle" @click="handleAddCourse(scope.row)">添加课程</span>
            <span
              v-if="fromType === 'classSetting'"
              :class="scope.row.type === 'INTEREST'
                ? 'item-handle'
                : 'item-handle-dis'
              "
              @click="
                scope.row.type === 'INTEREST' ? handleEdit(scope.row) : ''
              "
            >编辑班级</span>
            <br />
            <span v-if="fromType === 'classSetting'" class="item-handle-del" @click="handleRemove(scope.row)">删除班级</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 创建班级 -->
    <jionClass
      v-if="addClassShow"
      :show="addClassShow"
      :append-to-body="true"
      @close="addClassShow = false;_getUserClassList()"
    />

    <!-- 绑定学校 -->
    <bindSchool
      v-if="bindSchoolShow"
      :show="bindSchoolShow"
      :append-to-body="true"
      @close="bindSchoolShow = false"
    />

    <!-- 添加课程 -->
    <addCourse
      v-if="addCourseShow"
      :row="nowEditRow"
      :show="addCourseShow"
      :append-to-body="true"
      @close="addCourseShow = false"
      @added="addCourseShow = false;_getUserClassList()"
    />

    <NormalDialog
      v-if="editClassNameShow"
      width="30vw"
      :title="'修改班级名'"
      :dialog-visible="editClassNameShow"
      :is-center="true"
      @closeDialog="
        editClassNameShow = false
        clearItem()
      "
    >
      <el-input v-model="classNameChangeValue" class="w" placeholder="请输入班级名" />
      <template #footer>
        <div class="edu-btn" @click="edit">确定</div>
      </template>
    </NormalDialog>

    <NormalDialog
      v-if="inputStuListShow"
      width="30vw"
      height="25vw"
      :title="'导入学生名单'"
      :dialog-visible="inputStuListShow"
      :is-center="true"
      :has-footer="false"
      @closeDialog="closeInputStuList"
    >
      <div class="w">
        <div class="input-excel">
          <div class="input-box w">
            {{ fileName }}
          </div>
          <div>
            <el-upload
              ref="upload"
              :action="uploadUrl"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
              :on-change="handleChange"
              :auto-upload="false"
              :file-list="fileList"
              :data="{ user_id: inputInfo.userId }"
            >
              <el-button class="item-handle" type="text">选择文件</el-button>
            </el-upload>
          </div>
        </div>
        <div class="w flex" style="align-items: center">
          <el-button class="item-handle" type="text" @click="donwloadExcel">下载模版Excel</el-button>
          <div class="item-tip">（为确保准确识别，请下载此模版用于名单录入）</div>
        </div>
        <div class="w flex justify-center mb20">
          <div :class="fileList.length > 0 ? 'btn-active' : 'btn'" @click="inputSubmit">{{ isUploading ? '正在上传' : '上传' }}
          </div>
        </div>
      </div>
    </NormalDialog>

    <NormalDialog
      v-if="classInfoShow"
      width="35vw"
      :title="'班级介绍'"
      :dialog-visible="classInfoShow"
      :is-center="true"
      @closeDialog="closeClassInfo"
    >
      <div class="w mb10">
        <el-select v-model="subjectVal" placeholder="请选择科目" @change="changeSubject">
          <el-option v-for="item in subjectList" :key="item.strType" :label="item.name" :value="item.strType" />
        </el-select>
        <el-input
          v-model="textarea"
          class="mt10"
          type="textarea"
          rows="8"
          :disabled="!subjectVal"
          resize="none"
          placeholder="介绍班级整体情况，重点描述班级平均成绩水平"
          maxlength="200"
          show-word-limit
        />
      </div>
      <template #footer>
        <div class="edu-btn" @click="submitClassInfo">确定</div>
      </template>
    </NormalDialog>

    <el-drawer
      title=""
      :visible.sync="drawer"
      :direction="'btt'"
      :show-close="true"
      :with-header="false"
      :size="'100%'"
      :custom-class="'grade-drawer'"
      :close-on-press-escape="false"
    >

      <div class="top">
        <svg-icon icon-class="close" class-name="close-svg" @click="drawer = false" />
      </div>
      <div class="bottom">
        <div class="drawer-title">
          <div class="line"></div>
          <div>学生名单</div>
          <div class="stu-len">
            共{{ stuLists.length }}人
          </div>
          <div class="btn-active" @click="drawer = false; inputStuListShow = true;">导入名单</div>
        </div>
        <div class="drawer-detail">
          <div v-for="(item, index) in stuLists" :key="index" class="stu-item">
            {{ item }}
          </div>
        </div>
      </div>
    </el-drawer>

    <ComponentDialog
      :width="'30vw'"
      :title="'删除'"
      :dialog-visible="removeVisible"
      :is-center="true"
      @closeDialog="
        removeVisible = false
        clearItem()
      "
    >
      <div class="flex flex-col w items-center">
        <div class="f14" style="margin-bottom: 30px; line-height: 40px">
          删除班级后，该班级相关信息将全部清除不可找回， 确认删除吗？
        </div>
        <div class="flex justify-around w">
          <div
            class="edu-btn-opacity f14"
            @click="
              removeVisible = false
              clearItem()
            "
          >
            放弃
          </div>
          <div class="edu-btn f14" @click="remove">确定删除</div>
        </div>
      </div>
    </ComponentDialog>

    <NormalDialog
      v-if="codeShow"
      width="35vw"
      :title="codeInfo && codeInfo.name"
      :dialog-visible="codeShow"
      :is-center="true"
      @closeDialog="codeShow = false"
    >
      <div class="w flex flex-col items-center">
        <div ref="bill" class="w flex flex-col items-center">
          <div class="code-share-title flex items-center">{{ codeInfo && codeInfo.name }}</div>
          <div ref="qrCodeUrl" class="qrcode mb-10"></div>
          <div class="code-share flex items-center">
            班级二维码，保存或截图分享
          </div>
        </div>
        <div class="mt20 flex justify-center">
          <el-button type="text" class="f16" @click="saveImg">保存</el-button>
        </div>
      </div>
    </NormalDialog>
  </div>
</template>

<script>
import QRCode from 'qrcodejs2'
import html2canvas from 'html2canvas'
import moment from 'moment'
import NormalDialog from '@/components/classPro/NormalDialog/index.vue'
import ComponentDialog from '@/components/classPro/ComponentDialog'
import { mapGetters } from 'vuex'
import {
  getUserClassList,
  changeChildName,
  assistantDeleteClass,
  getSubjectConfig,
  updateClassIntro
} from '@/api/educational-api.js'
import { debounce } from '@/utils/index'
import bindSchool from '@/components/classPro/ClassBind/bindSchool.vue'
import addCourse from '@/components/classPro/ClassBind/addCourse.vue'
import jionClass from '@/components/classPro/ClassBind/jionClass.vue'

export default {
  components: {
    NormalDialog,
    ComponentDialog,
    bindSchool,
    addCourse,
    jionClass
  },
  filters: {
    formTime (val) {
      return moment(val).format('YYYY/MM/DD HH:mm')
    },
    classType (val) {
      const obj = {
        'ORGANIZATION': '行政班',
        'INTEREST': '兴趣班'
      }
      return obj[val]
    },
    techName (val) {
      const arr = []
      for (let i = 0; i < val.length; i++) {
        arr.push(val[i].displayName)
      }
      return arr.join('、')
    },
    stuList (val) {
      if (val) {
        if (val.studentList) {
          const arr = val.studentList.split(',')
          return arr.length + '人>'
        }
      }
      return ''
    }
  },
  props: {
    fromType: {
      type: String,
      default: '' // classSetting: 班级管理
    }
  },
  data () {
    return {
      code: null,
      codeInfo: null,
      codeShow: false,
      addClassShow: false,
      bindSchoolShow: false,
      editClassNameShow: false,
      addCourseShow: false,
      classInfoShow: false,
      classInfoNow: null,
      textarea: '',
      subjectList: [],
      subjectVal: '',
      classImage: null,
      tableData: [],
      tableDataMap: null,
      selectCrousUserId: '',
      classNameChangeValue: '',
      nowEditRow: null,
      hasWeeks: false,
      nowCourseList: [],
      removeVisible: false,
      stuLists: [],
      drawer: false,
      inputInfo: [],
      inputStuListShow: false,
      fileName: '',
      fileList: [],
      isUploading: false,
      uploadUrl: process.env.VUE_APP_ADMIN_API + '/home/<USER>/rosterImport'
    }
  },
  computed: {
    ...mapGetters([
      'school',
      'id'
    ])
  },
  mounted () {
    this._getUserClassList()
  },
  methods: {
    tableHeader (row, rowIndex) {
      return 'table-header'
    },
    rowClass (row, rowIndex) {
      return 'row-class'
    },
    cellClass (row, rowIndex) {
      return 'cell-class'
    },
    async _getUserClassList () {
      const { data } = await getUserClassList()
      this.tableData = data
      const map = new Map()
      for (let i = 0; i < data.length; i++) {
        map.set(data[i].userId, data[i])
      }
      this.tableDataMap = map
    },
    changeClassInfo (item) {
      this.classInfoNow = item
      this._getSubjectConfig()
      if (item.classImages) {
        const obj = {}
        item.classImages.forEach(e => {
          obj[e.subject] = e
        })
        this.classImage = obj
      } else {
        this.classImage = null
      }
      this.classInfoShow = true
    },
    changeSubject (item) {
      if (this.classImage && this.classImage[item]) {
        this.textarea = this.classImage[item].intro
      } else {
        this.textarea = ''
      }
    },
    submitClassInfo: debounce(async function () {
      if (!this.subjectVal) {
        this.$message.error('请选择科目')
        return
      }
      if (!this.textarea) {
        this.$message.error('请填写班级介绍')
        return
      }
      await updateClassIntro({
        subject: this.subjectVal,
        childUserId: this.classInfoNow.userId,
        intro: this.textarea
      })
      this.$message.success('修改成功')
      this._getUserClassList()
      this.closeClassInfo()
    }, 3000, true),
    async _getSubjectConfig () {
      const { data } = await getSubjectConfig({ getSubjectConfig: 'TEACHER_CHOSE' })
      this.subjectList = data
    },
    closeClassInfo () {
      this.classInfoShow = false
      this.textarea = ''
      this.subjectVal = ''
      this.classImage = null
    },
    handleEdit (row) {
      this.nowEditRow = row
      this.editClassNameShow = true
    },
    edit: debounce(async function () {
      if (!this.classNameChangeValue) {
        this.$message.error('请输入内容')
        return
      }
      await changeChildName({
        childUserId: this.nowEditRow.userId,
        childName: this.classNameChangeValue
      })
      this.editClassNameShow = false
      this.clearItem()
      this._getUserClassList()
    }, 3000, true),
    handleAddCourse (row) {
      this.nowEditRow = row
      this.addCourseShow = true
    },
    handleRemove (row) {
      this.nowEditRow = row
      this.removeVisible = true
    },
    remove: debounce(async function () {
      try {
        await assistantDeleteClass({ childUserId: this.nowEditRow.userId })
        this.removeVisible = false
        this._getUserClassList()
        this.clearItem()
      } catch (error) {
        this.removeVisible = false
        this.clearItem()
      }
    }, 3000, true),
    gotoDetail (row) {
      this.$router.push({ path: '/educational/classinfo?t=' + row.token })
    },
    clearItem () {
      this.value = ''
      this.classNameChangeValue = ''
      this.nowEditRow = null
      this.selectCrousUserId = ''
      this.hasWeeks = false
      this.nowCourseList = []
    },
    showStuLists (row) {
      this.drawer = true
      this.stuLists = []
      this.inputInfo = row
      if (row) {
        if (row.studentList) {
          const arr = row.studentList.split(',')
          this.stuLists = arr
        }
      }
    },
    handleDrawerClose (done) {
      done()
    },
    inputStu (row) {
      this.fileName = ''
      this.fileList = []
      this.inputInfo = row
      this.inputStuListShow = true
    },
    inputSubmit () {
      if (this.isUploading || this.fileList.length === 0) {
        return
      }
      this.isUploading = true
      if (this.fileList.length > 0) {
        this.$refs.upload.submit()
      } else {
        return
      }
    },
    handleAvatarSuccess (res, file) {
      if (res.status + '' === '1') {
        this.$message.success('导入成功')
      } else {
        this.$message.error(res.content)
      }
      this._getUserClassList()
      this.fileList = []
      this.fileName = ''
      this.inputStuListShow = false
      this.isUploading = false
    },
    handleChange (file, fileList) {
      const index = file.name.lastIndexOf('.')
      // 获取后缀
      const ext = file.name.substr(index + 1)
      const isXls = ['xls', 'xlsx'].indexOf(ext.toLowerCase()) !== -1
      if (!isXls) {
        this.$message.error('请上传Excel文件')
        this.fileList = []
        this.fileName = ''
      } else {
        this.fileList = [fileList[fileList.length - 1]]
        this.fileName = file.name
      }
    },
    closeInputStuList () {
      this.inputStuListShow = false
      this.fileList = []
      this.fileName = ''
    },
    beforeAvatarUpload (file) {
      const index = file.name.lastIndexOf('.')
      // 获取后缀
      const ext = file.name.substr(index + 1)
      const isXls = ['xls'].indexOf(ext.toLowerCase()) !== -1
      if (!isXls) {
        this.$message.error('请上传Excel文件')
        return Promise.reject()
      }
      return isXls
    },
    donwloadExcel () {
      const blobUrl = process.env.VUE_APP_ADMIN_API + '/Public/download/班级学生名单导入模板.xls'
      const downloadElement = document.createElement('a')
      downloadElement.href = blobUrl
      // 下载后文件名
      downloadElement.download = '班级学生名单导入模板.xls'
      document.body.appendChild(downloadElement)
      // 点击下载
      downloadElement.click()
      // 下载完成移除元素
      document.body.removeChild(downloadElement)
      if (window.URL.revokeObjectURL == null || window.URL.revokeObjectURL) {
        // 释放掉blob对象
        window.URL.revokeObjectURL(blobUrl)
      }
    },
    creatQrCode (row) {
      const origin = window.location.origin
      const url = `${origin}/#/parent/hassClassInfo?isScan=1&classId=${row.userId}&className=${row.name}&schoolId=${row.school && row.school.id}&schoolName=${row.school && row.school.name}`
      this.codeShow = true
      this.codeInfo = row
      this.$nextTick(
        () => {
          new QRCode(this.$refs.qrCodeUrl, {
            width: 200, // 二维码宽度
            height: 200, // 二维码高度
            text: url, // 浏览器地址url
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.H
          })
          this.$refs.qrCodeUrl.removeAttribute('title')
        }
      )
    },
    dataURLToBlob (dataurl) {
      const arr = dataurl.split(',')
      const mime = arr[0].match(/:(.*?);/)[1]
      const bstr = atob(arr[1])
      let n = bstr.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new Blob([u8arr], { type: mime })
    },
    saveImage () {
      const canvasID = this.$refs.bill
      const that = this
      const a = document.createElement('a')
      html2canvas(canvasID).then((canvas) => {
        const dom = document.body.appendChild(canvas)
        dom.style.display = 'none'
        a.style.display = 'none'
        document.body.removeChild(dom)
        const blob = that.dataURLToBlob(dom.toDataURL('image/png'))
        a.setAttribute('href', URL.createObjectURL(blob))
        // 这块是保存图片操作  可以设置保存的图片的信息
        a.setAttribute('download', that.codeInfo.name + '.png')
        document.body.appendChild(a)
        a.click()
        URL.revokeObjectURL(blob)
        document.body.removeChild(a)
      })
    },
    saveImg () {
      this.saveImage()
    }
  }
}
</script>

<style lang="scss" scoped>
.mt10 {
  margin-top: 10px;
}

.mb10 {
  margin-bottom: 10px;
}

.w70 {
  width: 70px;
}

.f14 {
  font-size: 14px;
}

.f20 {
  font-size: 20px;
}

.school-disc {
  margin-top: 10px;
}

.school-disc2 {
  width: 5px;
  height: 5px;
  background: #828282;
  border-radius: 50%;
  margin-right: 5px;
}

.grade-wrap {
  width: 100%;
  height: 100%;
  padding: 10px;

  .grade-title {
    height: 60px;
    border: 1px solid #c3d5f9;
    border-radius: 5px;
    padding: 0 20px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-L);
  }

  .table-box {
    height: calc(100% - 70px);
    padding: 0;

    .table-title {
      height: 50px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 500;
      font-size: var(--font-size-L);
      color: #1c1b1a;
      padding-right: 20px;

      .icon-i {
        width: 3px;
        height: 16px;
        background: #3479ff;
        border-radius: 1px;
        margin-right: 5px;
      }
    }
  }

  .btn,
  .btn-active {
    width: 80px;
    height: 28px;
    border-radius: 5px;
    font-size: var(--font-size-L);
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }

  .btn {
    background: #aaaaaa;
  }

  .btn-active {
    background: #3479ff;
  }

  .tr {
    text-align: right;
  }

  .tl {
    text-align: left;
  }

  .item-scope {
    width: 100%;
    font-size: var(--font-size-L);
    @include ellipses(1);
  }
  .item-tip {
    height: 100%;
    display: flex;
    align-items: center;
    font-size: var(--font-size-S);
    color: #595959;
  }

  .item-handle {
    color: #3479ff;
  }

  .item-handle-dis {
    color: #595959;
  }

  .item-handle-del {
    color: #ff3434;
  }

  .item-handle-del,
  .item-handle-dis,
  .item-handle {
    font-size: var(--font-size-L);
    cursor: pointer;
    margin-right: 10px;
    text-decoration: underline;
  }

  .mb10 {
    margin-bottom: 10px;
  }

  .f14 {
    font-size: 14px;
  }
}

.pop-box {
  max-height: 300px;
  overflow-y: auto;

  .item-scope {
    margin-bottom: 10px;
    font-size: var(--font-size-L); // 避免自动适配

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>

<style lang="scss" scoped>
.grade-wrap {
  .table-header {
    font-weight: 400;
    font-size: var(--font-size-L);
    color: #3479ff;

    th {
      background: #f8faff;
    }

    .el-table__cell {
      border-bottom: none;
      padding: 0px;
      font-size: var(--font-size-L);
    }
  }

  .cell-class {
    font-size: 14px;
    //font-size: var(--font-size-L);
  }

  .row-class {
    font-weight: 400;
    font-size: var(--font-size-L);
    color: #0e0e0e;
    letter-spacing: 0.22px;
  }
}

.item-scope {
  @include ellipses(1);
}

.input-excel {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;

  .input-box {
    width: 300px;
    height: 40px;
    background: #FFFFFF;
    border: 1px solid #3479FF;
    border-radius: 3px;
    display: flex;
    align-items: center;
    padding: 0 15px;
  }
}

.mb20 {
  margin-bottom: 20px;
}

.top {
  //height: 400px;
  //display: flex;
  //padding: 338px 30px 0 0;
  position: fixed;
  bottom: 210px;
  right: 10px;

  .close-svg {
    width: 36px;
    height: 36px;
    margin-left: auto;
    cursor: pointer;
  }
}

.bottom {
  position: fixed;
  bottom: 0px;
  //height: calc(100% - 400px);
  height: 200px;
  width: 100%;
  background: #FFFFFF;
  border-radius: 40px 40px 0px 0px;
  padding: 20px 20px 0 20px;

  .drawer-title {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    font-weight: 500;
    font-size: var(--font-size-L);
    color: #1C1B1A;
    box-sizing: border-box;

    .line {
      width: 2px;
      height: 14px;
      background: #3479FF;
      margin-right: 10px;
    }

    .stu-len {
      margin-left: 20px;
      margin-right: 50px;
      font-weight: 500;
      font-size: var(--font-size-L);
      color: #3479FF;
    }
  }

  .drawer-detail {
    margin-top: 20px;
    width: 100%;
    height: calc(100% - 60px);
    //height: 8px;
    overflow-y: auto;

    .stu-item {
      padding: 20px 20px;
      box-sizing: border-box;
      font-size: var(--font-size-L);
      color: #19191A;
      display: inline-block;
    }
  }
}

.code-share {
  font-size: var(--font-size-L);
  padding: 10px 0;
  color: #000000;
}

.code-share-title {
  font-size: var(--font-size-L);
  font-weight: 500;
  padding: 10px 0;
  color: #000000;
  font-weight: 600;
}

.btn-jihuo {
  width: 140px;
  height: 35px;
  border-radius: 5px;
  font-size: var(--font-size-L);
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  background: #3479ff;
  margin-top: 50px;
}
.jihuo-text {
  color: #000;
  font-size: var(--font-size-L);
  margin-top: 150px;
  .fb {
    font-weight: 500;
  }
}
</style>

<style lang='scss'>
.grade-drawer {
  width: 100%;
  height: 100%;
}
</style>
