<template>
  <div class="grade-wrap">
    <div class="grade-title">
      <template v-if="school">
        <div>{{ school.name }}</div>
        <div class="btn">激活账号</div>
      </template>
      <template v-else>
        <div>暂未激活账号</div>
        <div class="btn-active" @click="bindSchoolShow = true">激活账号</div>
      </template>
    </div>
    <div class="table-box">
      <div class="table-title">
        <div class="flex items-center">
          <div class="icon-i"></div>
          <div>班级列表</div>
        </div>
        <div>
          <div
            :class="school ? 'btn-active' : 'btn'"
            @click="school ? (addClassShow = true) : ''"
          >
            添加班级
          </div>
        </div>
      </div>
      <el-table
        :data="tableData"
        :header-row-style="{ background: '#F8FAFF' }"
        :header-cell-style="{
          background: '#F8FAFF',
          color: '#3479FF',
          border: 'none',
          'font-weight': '400'
        }"
        :header-row-class-name="tableHeader"
        :row-class-name="rowClass"
        :cell-class-name="cellClass"
        :height="'calc(100% - 70px)'"
        style="width: 100%"
      >
        <el-table-column
          prop="name"
          align="left"
          label="班级名称"
        >
          <template slot-scope="scope">
            <div class="flex items-center" @click="creatQrCode(scope.row)">
              <div>{{ scope.row.name }}</div>
              <div>
                <img style="vertical-align: middle;" src="../../../../assets/parent/assistant-code.svg" />
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="type" align="left" label="班级类型">
          <template slot-scope="scope">
            {{ scope.row.type | classType }}
          </template>
        </el-table-column>
        <el-table-column
          prop="assistantList"
          align="left"
          label="管理教师"
        >
          <template slot-scope="scope">
            {{ scope.row.assistantList | techName }}
          </template>
        </el-table-column>

        <el-table-column
          align="left"
          label="学生名单"
        >
          <template slot-scope="scope">
            <div class="pointer" @click="showStuLists(scope.row.userExtra)">{{ scope.row.userExtra | stuList }}</div>
            <el-button class="item-handle" type="text" @click="inputStu(scope.row)">导入学生</el-button>
          </template>
        </el-table-column>

        <el-table-column
          align="left"
          label="班级介绍"
        >
          <template slot-scope="scope">
            <el-button class="item-handle" type="text" @click="changeClassInfo(scope.row)">介绍</el-button>
          </template>
        </el-table-column>

        <el-table-column
          prop="createdAt"
          align="left"
          label="创建时间"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.createdAt | formTime }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="courseList" align="left" label="在学课程">
          <template slot-scope="scope">
            <el-popover placement="bottom-start">
              <div class="pop-box">
                <div
                  v-for="item in scope.row.courseList"
                  :key="item.id"
                  class="item-scope"
                >
                  {{
                    (item.course && item.course.name) ||
                      (item.aicourse && item.aicourse.title)
                  }}
                </div>
              </div>
              <div slot="reference">
                <div
                  v-if="scope.row.courseList && scope.row.courseList[0]"
                  class="item-scope"
                >
                  {{
                    (scope.row.courseList[0].course &&
                      scope.row.courseList[0].course.name) ||
                      (scope.row.courseList[0].aicourse &&
                        scope.row.courseList[0].aicourse.title)
                  }}
                </div>
                <div
                  v-if="scope.row.courseList && scope.row.courseList[1]"
                  class="item-scope"
                >
                  {{
                    (scope.row.courseList[1].course &&
                      scope.row.courseList[1].course.name) ||
                      (scope.row.courseList[1].aicourse &&
                        scope.row.courseList[1].aicourse.title)
                  }}
                </div>
                <div
                  v-show="
                    scope.row.courseList && scope.row.courseList.length > 2
                  "
                  class="tl"
                >
                  ...
                </div>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column
          prop="lessonModel"
          align="left"
          label="课程形式"
        >
          <template slot-scope="scope">
            <el-popover placement="bottom-start">
              <div class="pop-box">
                <div
                  v-for="item in scope.row.lessonModel"
                  :key="item"
                  class="item-scope"
                >
                  {{ item }}
                </div>
              </div>
              <div slot="reference">
                <div v-if="scope.row.lessonModel[0]" class="item-scope">
                  {{ scope.row.lessonModel[0] }}
                </div>
                <div v-if="scope.row.lessonModel[1]" class="item-scope">
                  {{ scope.row.lessonModel[1] }}
                </div>
                <div v-show="scope.row.lessonModel.length > 2" class="tl">
                  ...
                </div>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="" align="left" label="操作">
          <template slot-scope="scope">
            <!-- <span class="item-handle" @click="gotoDetail(scope.row)">详情</span> -->
            <span
              class="item-handle"
              @click="handleAddCourse(scope.row)"
            >添加课程</span>
            <span
              :class="
                scope.row.type === 'INTEREST'
                  ? 'item-handle'
                  : 'item-handle-dis'
              "
              @click="
                scope.row.type === 'INTEREST' ? handleEdit(scope.row) : ''
              "
            >编辑</span>
            <span
              class="item-handle-del"
              @click="handleRemove(scope.row)"
            >删除</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <NormalDialog
      v-if="addClassShow"
      width="340px"
      :title="'添加班级'"
      :dialog-visible="addClassShow"
      :is-center="true"
      @closeDialog="addClassShow = false"
    >
      <div class="w flex justify-around">
        <div class="edu-btn-opacity" @click="enterAdminClassShow = true">
          加入行政班
        </div>
        <div class="edu-btn-opacity" @click="enterInterestClassShow = true">
          添加兴趣班
        </div>
      </div>
    </NormalDialog>
    <NormalDialog
      v-if="enterAdminClassShow"
      width="400px"
      :title="'加入行政班'"
      :dialog-visible="enterAdminClassShow"
      :append-to-body="true"
      :is-center="true"
      @closeDialog="
        enterAdminClassShow = false
        clearItem()
      "
    >
      <div class="w flex-col">
        <div class="flex items-center" style="margin-bottom: 10px;">
          <div>选择年级：</div>
          <el-select
            v-model="gradeValue"
            style="flex: 1"
            class="w"
            placeholder="请选择"
          >
            <el-option
              v-for="item in gradeOption"
              :key="item.year"
              :label="item.gradName"
              :value="`${item.year},${item.level}`"
            />
          </el-select>
        </div>
        <div class="flex items-center">
          <div>选择班级：</div>
          <el-select
            v-model="teamValue"
            style="flex: 1"
            class="w"
            placeholder="请选择"
          >
            <el-option
              v-for="item in classNameOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>
      <template #footer>
        <div class="edu-btn" @click="addClass('ORGANIZATION')">确定</div>
      </template>
    </NormalDialog>

    <NormalDialog
      v-if="enterInterestClassShow"
      width="400px"
      :title="'添加兴趣班'"
      :dialog-visible="enterInterestClassShow"
      :append-to-body="true"
      :is-center="true"
      @closeDialog="
        enterInterestClassShow = false
        clearItem()
      "
    >
      <div class="w flex-col">
        <div class="flex items-center mb10">
          <div>班级名称：</div>
          <el-input
            v-model="nameValue"
            style="flex: 1"
            class="w"
            placeholder="请输入班级名称"
          />
        </div>
      </div>
      <template #footer>
        <div class="edu-btn" @click="addClass('INTEREST')">确定</div>
      </template>
    </NormalDialog>

    <NormalDialog
      v-if="bindSchoolShow"
      width="400px"
      :title="'激活账号'"
      :dialog-visible="bindSchoolShow"
      :is-center="true"
      @closeDialog="
        bindSchoolShow = false
        clearItem()
      "
    >
      <div class="flex flex-col">
        <div class="mb10">
          <el-input v-model="schoolCode" class="w" placeholder="请输入学校课程激活码" />
        </div>
        <div style="color: #828282">
          备注：
          <div class="school-disc flex items-center">
            <div class="school-disc2"></div>
            仅支持已合作的学校
          </div>
          <div class="school-disc flex items-center">
            <div class="school-disc2"></div>
            如没有激活码，请联系学校管理员获取
          </div>
        </div>
      </div>
      <template #footer>
        <div class="edu-btn" @click="bindSchool">确定</div>
      </template>
    </NormalDialog>

    <NormalDialog
      v-if="editClassNameShow"
      width="400px"
      :title="'修改班级名'"
      :dialog-visible="editClassNameShow"
      :is-center="true"
      @closeDialog="
        editClassNameShow = false
        clearItem()
      "
    >
      <el-input
        v-model="classNameChangeValue"
        class="w"
        placeholder="请输入班级名"
      />
      <template #footer>
        <div class="edu-btn" @click="edit">确定</div>
      </template>
    </NormalDialog>

    <NormalDialog
      v-if="addCourseShow"
      width="550px"
      :title="'添加课程'"
      :dialog-visible="addCourseShow"
      :is-center="true"
      @closeDialog="
        addCourseShow = false
        clearItem()
      "
    >
      <div class="w flex-col">
        <div class="flex items-center mb10">
          <div>选择班级：</div>
          <el-select
            v-model="selectClassUserId"
            style="flex: 1"
            class="w"
            :disabled="disableSelectClassUserId"
            placeholder="请选择"
            @change="getCourseList"
          >
            <el-option
              v-for="item in tableData"
              :key="item.id"
              :label="item.name"
              :value="item.userId"
            />
          </el-select>
        </div>
        <div class="flex items-center">
          <div>选择课包：</div>
          <el-select
            v-model="selectCrousUserId"
            style="flex: 1"
            class="w"
            :disabled="!selectClassUserId"
            placeholder="请选择"
          >
            <el-option
              v-for="item in crouseList"
              :key="item.aiCourse && item.aiCourse.id"
              :label="item.aiCourse && item.aiCourse.title"
              :value="item.aiCourse && item.aiCourse.id"
              :disabled="item.has"
            >
              <div class="flex w" style="width: 300px">
                <div style="width: calc(100% - 60px)">
                  <div class="item-scope w" style="display: inline-block">
                    {{ item.aiCourse && item.aiCourse.title }}
                  </div>
                </div>
                <div
                  class="tr"
                  style="width: 60px"
                  :style="item.has ? 'color: #636363;' : 'color: #3479FF;'"
                >
                  {{ item.has ? '已添加' : '添加' }}
                </div>
              </div>
            </el-option>
          </el-select>
        </div>
        <div class="flex" style="padding: 10px 0 0 0;">
          <div class="w100"></div>
          <div class="f12" style="color:#EB5757;">*添加自己班级课程，非所属班课程请勿添加</div>
        </div>
        <div class="flex items-start" style="padding-top: 10px">
          <div>计划授课：</div>
          <div class="flex flex-col" style="min-height: 40px; flex: 1">
            <el-switch
              v-model="hasWeeks"
              active-color="#3479FF"
              inactive-color="#e9eef5"
            />
            <div v-if="hasWeeks" style="padding-top: 20px">
              <el-checkbox-group v-model="weeksList">
                <el-checkbox label="每周一" />
                <el-checkbox label="每周二" />
                <el-checkbox label="每周三" />
                <el-checkbox label="每周四" />
                <el-checkbox label="每周五" />
              </el-checkbox-group>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="edu-btn" @click="addCourse">确定</div>
      </template>
    </NormalDialog>

    <NormalDialog
      v-if="inputStuListShow"
      width="550px"
      :title="'导入学生名单'"
      :dialog-visible="inputStuListShow"
      :is-center="true"
      :has-footer="false"
      @closeDialog="closeInputStuList"
    >
      <div class="w">
        <div class="input-excel">
          <div class="input-box w">
            {{ fileName }}
          </div>
          <div>
            <el-upload
              ref="upload"
              :action="uploadUrl"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
              :on-change="handleChange"
              :auto-upload="false"
              :file-list="fileList"
              :data="{ user_id: inputInfo.userId }"
            >
              <el-button class="item-handle" type="text">选择文件</el-button>
            </el-upload>
          </div>
        </div>
        <div class="w flex" style="align-items: center">
          <el-button class="item-handle" type="text" @click="donwloadExcel">下载模版Excel</el-button>
          <div class="item-tip">（为确保准确识别，请下载此模版用于名单录入）</div>
        </div>
        <div class="w flex justify-center mb20">
          <div :class="fileList.length > 0 ? 'btn-active':'btn'" @click="inputSubmit">{{ isUploading ? '正在上传':'上传' }}</div>
        </div>
      </div>
    </NormalDialog>

    <NormalDialog
      v-if="classInfoShow"
      width="500px"
      :title="'班级介绍'"
      :dialog-visible="classInfoShow"
      :is-center="true"
      @closeDialog="closeClassInfo"
    >
      <div class="w mb10">
        <el-select
          v-model="subjectVal"
          placeholder="请选择科目"
          @change="changeSubject"
        >
          <el-option
            v-for="item in subjectList"
            :key="item.strType"
            :label="item.name"
            :value="item.strType"
          />
        </el-select>
        <el-input
          v-model="textarea"
          class="mt10"
          type="textarea"
          rows="8"
          :disabled="!subjectVal"
          resize="none"
          placeholder="介绍班级整体情况，重点描述班级平均成绩水平"
          maxlength="200"
          show-word-limit
        />
      </div>
      <template #footer>
        <div class="edu-btn" @click="submitClassInfo">确定</div>
      </template>
    </NormalDialog>

    <el-drawer
      title=""
      :visible.sync="drawer"
      :direction="'btt'"
      :show-close="true"
      :with-header="false"
      :size="'100%'"
      :custom-class="'grade-drawer'"
      :close-on-press-escape="false"
    >

      <div class="top">
        <svg-icon icon-class="close" class-name="close-svg" @click="drawer = false" />
      </div>
      <div class="bottom">
        <div class="drawer-title">
          <div class="line"></div>
          <div>学生名单</div>
          <div class="stu-len">
            共{{ stuLists.length }}人
          </div>
          <div class="btn-active" @click="drawer=false;inputStuListShow = true;">导入名单</div>
        </div>
        <div class="drawer-detail">
          <div v-for="(item, index) in stuLists" :key="index" class="stu-item">
            {{ item }}
          </div>
        </div>
      </div>
    </el-drawer>

    <ComponentDialog
      :width="'30vw'"
      :title="'删除'"
      :dialog-visible="removeVisible"
      :is-center="true"
      @closeDialog="
        removeVisible = false
        clearItem()
      "
    >
      <div class="flex flex-col w items-center">
        <div class="f14" style="margin-bottom: 20px; line-height: 20px">
          删除班级后，该班级相关信息将全部清除不可找回， 确认删除吗？
        </div>
        <div class="flex justify-around w">
          <div
            class="edu-btn-opacity f14"
            @click="
              removeVisible = false
              clearItem()
            "
          >
            放弃
          </div>
          <div class="edu-btn f14" @click="remove">确定删除</div>
        </div>
      </div>
    </ComponentDialog>

    <NormalDialog
      v-if="tipsShow"
      width="500px"
      :title="'提示'"
      :dialog-visible="tipsShow"
      :is-center="true"
      @closeDialog="tipsShow = false"
    >
      <div class="w mb10 flex flex-col items-center">
        <div class="mb10 f20" style="color: #000;">该课程添加次数已用完</div>
        <img width="200" src="../../../../assets/images/kefuCode.png" />
        <div class="f14 mt10">扫码联系客服增加课程使用额度</div>
      </div>
    </NormalDialog>

    <NormalDialog
      v-if="codeShow"
      width="500px"
      :title="codeInfo && codeInfo.name"
      :dialog-visible="codeShow"
      :is-center="true"
      @closeDialog="codeShow = false"
    >
      <div class="w flex flex-col items-center">
        <div ref="bill" class="w flex flex-col items-center">
          <div class="code-share-title flex items-center">{{ codeInfo && codeInfo.name }}</div>
          <div ref="qrCodeUrl" class="qrcode mb-10"></div>
          <div class="code-share flex items-center">
            班级二维码，保存或截图分享
          </div>
        </div>
        <div class="mt20 flex justify-center">
          <el-button type="text" class="f16" @click="saveImg">保存</el-button>
        </div>
      </div>
    </NormalDialog>
  </div>
</template>

<script>
import QRCode from 'qrcodejs2'
import html2canvas from 'html2canvas'
import moment from 'moment'
import NormalDialog from '@/components/classPro/NormalDialog/index.vue'
import ComponentDialog from '@/components/classPro/ComponentDialog'
// import { mapGetters } from 'vuex'
import { getInfoIfream } from '@/api/user-api'

import {
  assistantBindSchool,
  getUserClassList,
  assistantAddClass,
  changeChildName,
  getSchoolCourseList,
  addCourseToClass,
  assistantDeleteClass,
  getSubjectConfig,
  updateClassIntro
} from '@/api/educational-api.js'
import { debounce } from '@/utils/index'
export default {
  components: { NormalDialog, ComponentDialog },
  filters: {
    formTime (val) {
      return moment(val).format('YYYY/MM/DD HH:mm')
    },
    classType (val) {
      const obj = {
        'ORGANIZATION': '行政班',
        'INTEREST': '兴趣班'
      }
      return obj[val]
    },
    techName (val) {
      const arr = []
      for (let i = 0; i < val.length; i++) {
        arr.push(val[i].displayName)
      }
      return arr.join('、')
    },
    stuList (val) {
      if (val) {
        if (val.studentList) {
          const arr = val.studentList.split(',')
          return arr.length + '人>'
        }
      }
      return ''
    }
  },
  data () {
    return {
      code: null,
      codeInfo: null,
      codeShow: false,
      tipsShow: false,
      addClassShow: false,
      enterAdminClassShow: false,
      enterInterestClassShow: false,
      bindSchoolShow: false,
      editClassNameShow: false,
      addCourseShow: false,
      disableSelectClassUserId: false,
      classInfoShow: false,
      classInfoNow: null,
      textarea: '',
      subjectList: [],
      subjectVal: '',
      classImage: null,
      tableData: [],
      tableDataMap: null,
      crouseList: [],
      selectCrousUserId: '',
      schoolCode: '',
      gradeValue: '',
      teamValue: '',
      nameValue: '',
      classNameChangeValue: '',
      nowEditRow: null,
      selectClassUserId: '',
      hasWeeks: false,
      weeksList: [],
      nowCourseList: [],
      removeVisible: false,
      stuLists: [],
      drawer: false,
      inputInfo: [],
      inputStuListShow: false,
      fileName: '',
      fileList: [],
      isUploading: false,
      uploadUrl: process.env.VUE_APP_ADMIN_API + '/home/<USER>/rosterImport',
      token: '',
      school: null,
      id: ''
    }
  },
  computed: {
    // ...mapGetters([
    //   'school',
    //   'id'
    // ]),
    gradeOption () {
      const mouth = moment().month() + 1
      let year = moment().year()
      if (mouth < 9) {
        year = year - 1
      }
      const arr = ['一年级', '二年级', '三年级', '四年级', '五年级', '六年级']
      // 初中
      const arr2 = ['七年级', '八年级', '九年级']
      const obj = []
      arr.forEach((val, index) => {
        obj.push({
          gradName: val + (year - index),
          year: year - index,
          grad: val,
          level: index + 1
        })
      })
      arr2.forEach((val, index) => {
        obj.push({
          gradName: val + (year - index),
          year: year - index,
          grad: val,
          level: 6 + index + 1
        })
      })
      return obj
    },
    classNameOption () {
      const obj = []
      for (let i = 1; i < 51; i++) {
        obj.push({
          value: i,
          label: i + '班'
        })
      }
      return obj
    }
  },
  async created () {
    this.token = 'Bearer ' + this.$route.query.token
    await this._getInfoIfream()
  },
  mounted () {
    this._getUserClassList()
  },
  methods: {
    async _getInfoIfream () {
      const { data } = await getInfoIfream({ 'userType': 'ASSISTANT' }, this.token)
      this.school = data.school
      this.id = data.id
    },
    tableHeader (row, rowIndex) {
      return 'table-header'
    },
    rowClass (row, rowIndex) {
      return 'row-class'
    },
    cellClass (row, rowIndex) {
      return 'cell-class'
    },
    async _getUserClassList () {
      const { data } = await getUserClassList({}, this.token)
      this.tableData = data
      const map = new Map()
      for (let i = 0; i < data.length; i++) {
        map.set(data[i].userId, data[i])
      }
      this.tableDataMap = map
    },
    addClass: debounce(async function (type) {
      const obj = {
        type: type
      }
      if (type === 'INTEREST') {
        if (!this.nameValue) {
          this.$message.error('请填写内容')
          return
        }
        obj.name = this.nameValue
      } else if (type === 'ORGANIZATION') {
        if (!(this.teamValue && this.gradeValue)) {
          this.$message.error('请填写内容')
          return
        }
        obj.grade = this.gradeValue.split(',')[0]
        obj.level = this.gradeValue.split(',')[1]
        obj.team = this.teamValue
      }
      const { data } = await assistantAddClass(obj, this.token)
      this.addClassShow = false
      this.enterInterestClassShow = false
      this.enterAdminClassShow = false
      this.clearItem()
      this._getUserClassList()
      // 显示二维码
      this.$nextTick(() => {
        this.creatQrCode(data)
      })
    }, 3000, true),
    bindSchool: debounce(async function () {
      if (this.schoolCode) {
        try {
          await assistantBindSchool({
            schoolCode: this.schoolCode
          }, this.token)
          this.schoolCode = ''
          this.$store.dispatch('user/GetInfo')
          this.bindSchoolShow = false
          this.$message.success('激活成功')
        } catch (error) {
          this.bindSchoolShow = false
        }
      }
    }, 3000, true),
    changeClassInfo (item) {
      this.classInfoNow = item
      this._getSubjectConfig()
      if (item.classImages) {
        const obj = {}
        item.classImages.forEach(e => {
          obj[e.subject] = e
        })
        this.classImage = obj
      } else {
        this.classImage = null
      }
      this.classInfoShow = true
    },
    changeSubject (item) {
      if (this.classImage && this.classImage[item]) {
        this.textarea = this.classImage[item].intro
      } else {
        this.textarea = ''
      }
    },
    submitClassInfo: debounce(async function () {
      if (!this.subjectVal) {
        this.$message.error('请选择科目')
        return
      }
      if (!this.textarea) {
        this.$message.error('请填写班级介绍')
        return
      }
      await updateClassIntro({
        subject: this.subjectVal,
        childUserId: this.classInfoNow.userId,
        intro: this.textarea
      }, this.token)
      this.$message.success('修改成功')
      this._getUserClassList()
      this.closeClassInfo()
    }, 3000, true),
    async _getSubjectConfig () {
      const { data } = await getSubjectConfig({ getSubjectConfig: 'TEACHER_CHOSE' })
      this.subjectList = data
    },
    closeClassInfo () {
      this.classInfoShow = false
      this.textarea = ''
      this.subjectVal = ''
      this.classImage = null
    },
    handleEdit (row) {
      this.nowEditRow = row
      this.editClassNameShow = true
    },
    edit: debounce(async function () {
      if (!this.classNameChangeValue) {
        this.$message.error('请输入内容')
        return
      }
      await changeChildName({
        childUserId: this.nowEditRow.userId,
        childName: this.classNameChangeValue
      }, this.token)
      this.editClassNameShow = false
      this.clearItem()
      this._getUserClassList()
    }, 3000, true),
    handleAddCourse (row) {
      this.nowEditRow = row
      this.addCourseShow = true
      this.selectClassUserId = row.userId
      this.getCourseList(row.userId)
      this.disableSelectClassUserId = true
    },
    async getCourseList (val) {
      this.nowEditRow = this.tableDataMap.get(val)
      const { data } = await getSchoolCourseList({
        studentId: val
      }, this.token)
      const map = new Map()
      data.forEach(element => {
        map.set(element.courseId, element.courseType)
      })
      this.nowCourseList = map
      this.crouseList = data
    },
    addCourse: debounce(async function () {
      if (!(this.nowEditRow && this.nowEditRow.userId && this.selectCrousUserId)) {
        this.$message.error('有内容未填写')
        return
      }
      const obj = {
        studentId: this.nowEditRow.userId,
        courseId: this.selectCrousUserId,
        courseType: this.nowCourseList.get(this.selectCrousUserId)
        // weekdays: ''
      }
      if (this.hasWeeks) {
        if (this.weeksList.length === 0) {
          this.$message.error('有内容未填写')
          return
        }
        const srtObj = {
          '每周一': 1,
          '每周二': 2,
          '每周三': 3,
          '每周四': 4,
          '每周五': 5,
          '每周六': 6,
          '每周日': 7
        }
        const arr = []
        for (let i = 0; i < this.weeksList.length; i++) {
          arr.push(srtObj[this.weeksList[i]])
        }
        obj.weekdays = arr.join(',')
      }
      try {
        await addCourseToClass(obj, this.token)
        this.addCourseShow = false
        this._getUserClassList()
        this.clearItem()
      } catch (error) {
        if (error.code === 910) {
          this.tipsShow = true
        }
      }
    }, 3000, true),
    handleRemove (row) {
      this.nowEditRow = row
      this.removeVisible = true
    },
    remove: debounce(async function () {
      try {
        await assistantDeleteClass({ childUserId: this.nowEditRow.userId }, this.token)
        this.removeVisible = false
        this._getUserClassList()
        this.clearItem()
      } catch (error) {
        this.removeVisible = false
        this.clearItem()
      }
    }, 3000, true),
    gotoDetail (row) {
      this.$router.push({ path: '/educational/classinfo?t=' + row.token })
    },
    clearItem () {
      this.schoolCode = ''
      this.value = ''
      this.gradeValue = ''
      this.teamValue = ''
      this.nameValue = ''
      this.classNameChangeValue = ''
      this.nowEditRow = null
      this.disableSelectClassUserId = false
      this.selectClassUserId = ''
      this.selectCrousUserId = ''
      this.crouseList = []
      this.hasWeeks = false
      this.weeksList = []
      this.nowCourseList = []
    },
    showStuLists (row) {
      this.drawer = true
      this.stuLists = []
      this.inputInfo = row
      if (row) {
        if (row.studentList) {
          const arr = row.studentList.split(',')
          this.stuLists = arr
        }
      }
    },
    handleDrawerClose (done) {
      done()
    },
    inputStu (row) {
      this.fileName = ''
      this.fileList = []
      this.inputInfo = row
      this.inputStuListShow = true
    },
    inputSubmit () {
      if (this.isUploading || this.fileList.length === 0) {
        return
      }
      this.isUploading = true
      if (this.fileList.length > 0) {
        this.$refs.upload.submit()
      } else {
        return
      }
    },
    handleAvatarSuccess (res, file) {
      if (res.status + '' === '1') {
        this.$message.success('导入成功')
      } else {
        this.$message.error(res.content)
      }
      this._getUserClassList()
      this.fileList = []
      this.fileName = ''
      this.inputStuListShow = false
      this.isUploading = false
    },
    handleChange (file, fileList) {
      const index = file.name.lastIndexOf('.')
      // 获取后缀
      const ext = file.name.substr(index + 1)
      const isXls = ['xls', 'xlsx'].indexOf(ext.toLowerCase()) !== -1
      if (!isXls) {
        this.$message.error('请上传Excel文件')
        this.fileList = []
        this.fileName = ''
      } else {
        this.fileList = [fileList[fileList.length - 1]]
        this.fileName = file.name
      }
    },
    closeInputStuList () {
      this.inputStuListShow = false
      this.fileList = []
      this.fileName = ''
    },
    beforeAvatarUpload (file) {
      const index = file.name.lastIndexOf('.')
      // 获取后缀
      const ext = file.name.substr(index + 1)
      const isXls = ['xls'].indexOf(ext.toLowerCase()) !== -1
      if (!isXls) {
        this.$message.error('请上传Excel文件')
        return Promise.reject()
      }
      return isXls
    },
    donwloadExcel () {
      const blobUrl = process.env.VUE_APP_ADMIN_API + '/Public/download/班级学生名单导入模板.xls'
      const downloadElement = document.createElement('a')
      downloadElement.href = blobUrl
      // 下载后文件名
      downloadElement.download = '班级学生名单导入模板.xls'
      document.body.appendChild(downloadElement)
      // 点击下载
      downloadElement.click()
      // 下载完成移除元素
      document.body.removeChild(downloadElement)
      if (window.URL.revokeObjectURL == null || window.URL.revokeObjectURL) {
        // 释放掉blob对象
        window.URL.revokeObjectURL(blobUrl)
      }
    },
    creatQrCode (row) {
      const origin = window.location.origin
      const url = `${origin}/#/parent/hassClassInfo?isScan=1&classId=${row.userId}&className=${row.name}&schoolId=${row.school && row.school.id}&schoolName=${row.school && row.school.name}`
      this.codeShow = true
      this.codeInfo = row
      this.$nextTick(
        () => {
          new QRCode(this.$refs.qrCodeUrl, {
            width: 200, // 二维码宽度
            height: 200, // 二维码高度
            text: url, // 浏览器地址url
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.H
          })
          this.$refs.qrCodeUrl.removeAttribute('title')
        }
      )
    },
    dataURLToBlob (dataurl) {
      const arr = dataurl.split(',')
      const mime = arr[0].match(/:(.*?);/)[1]
      const bstr = atob(arr[1])
      let n = bstr.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new Blob([u8arr], { type: mime })
    },
    saveImage () {
      const canvasID = this.$refs.bill
      const that = this
      const a = document.createElement('a')
      html2canvas(canvasID).then((canvas) => {
        const dom = document.body.appendChild(canvas)
        dom.style.display = 'none'
        a.style.display = 'none'
        document.body.removeChild(dom)
        const blob = that.dataURLToBlob(dom.toDataURL('image/png'))
        a.setAttribute('href', URL.createObjectURL(blob))
        // 这块是保存图片操作  可以设置保存的图片的信息
        a.setAttribute('download', that.codeInfo.name + '.png')
        document.body.appendChild(a)
        a.click()
        URL.revokeObjectURL(blob)
        document.body.removeChild(a)
      })
    },
    saveImg () {
      this.saveImage()
    }
  }
}
</script>

<style lang="scss" scoped>

.mt10 {
  margin-top: 10px;
}
.mb10 {
  margin-bottom: 10px;
}

.w70 {
  width: 70px;
}
.f14 {
  font-size: 14px;
}

.f20 {
  font-size: 20px;
}
.school-disc {
  margin-top: 10px;
}
.school-disc2 {
  width: 5px;
  height: 5px;
  background: #828282;
  border-radius: 50%;
  margin-right: 5px;
}
.grade-wrap {
  width: 100%;
  height: 100%;
  .grade-title {
    height: 70px;
    border: 1px solid #c3d5f9;
    border-radius: 5px;
    padding: 0 20px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .table-box {
    height: calc(100% - 70px);
    padding: 0 20px 0 0;
    .table-title {
      height: 60px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 500;
      font-size: 16px;
      color: #1c1b1a;
      padding-right: 20px;
      .icon-i {
        width: 3px;
        height: 16px;
        background: #3479ff;
        border-radius: 1px;
        margin-right: 5px;
      }
    }
  }

  .btn,
  .btn-active {
    width: 100px;
    height: 35px;
    border-radius: 5px;
    font-size: 14px;
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
  .btn {
    background: #aaaaaa;
  }

  .btn-active {
    background: #3479ff;
  }
  .tr {
    text-align: right;
  }
  .tl {
    text-align: left;
  }

  .item-scope {
    width: 100%;
    font-size: 14px;
    @include ellipses(1);
  }

  .item-tip {
    height: 100%;
    display: flex;
    align-items: center;
    font-size: var(--font-size-S);
    color: #595959;
  }

  .item-handle {
    color: #3479ff;
  }

  .item-handle-dis {
    color: #595959;
  }

  .item-handle-del {
    color: #ff3434;
  }

  .item-handle-del,
  .item-handle-dis, .item-handle {
    font-size: 14px;
    cursor: pointer;
    margin-right: 10px;
    text-decoration: underline;
  }

  .mb10 {
    margin-bottom: 10px;
  }

  .f14 {
    font-size: 14px;
  }
}

.pop-box {
  max-height: 300px;
  overflow-y: auto;
  .item-scope {
    margin-bottom: 10px;
    font-size: 14PX; // 避免自动适配
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>

<style lang="scss" scoped>
.grade-wrap {
  .table-header {
    font-weight: 400;
    font-size: 16px;
    color: #3479ff;

    th {
      background: #f8faff;
    }

    .el-table__cell {
      border-bottom: none;
    }
  }

  .cell-class {
    font-size: 14px;
  }

  .row-class {
    font-weight: 400;
    font-size: 14px;
    color: #0e0e0e;
    letter-spacing: 0.22px;
  }
}
.item-scope {
  @include ellipses(1);
}

.input-excel {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  .input-box {
    width: 300px;
    height: 40px;
    background: #FFFFFF;
    border: 1px solid #3479FF;
    border-radius: 3px;
    display: flex;
    align-items: center;
    padding: 0 15px;
  }
}

.mb20 {
  margin-bottom: 20px;
}

.top {
    height: 400px;
    display: flex;
    padding: 338px 30px 0 0;
    .close-svg {
        width: 36PX !important;
        height: 36PX !important;
        margin-left: auto;
        cursor: pointer;
    }
}

.bottom {
    height: calc(100% - 400px);
    width: 100%;
    background: #FFFFFF;
    border-radius: 40px 40px 0px 0px;
    padding: 20px 20px 0 20px;

    .drawer-title {
      width: 100%;
      height: 40px;
      display: flex;
      align-items: center;
      font-weight: 500;
      font-size: 16px;
      color: #1C1B1A;
      box-sizing: border-box;

      .line {
        width: 2px;
        height: 14px;
        background: #3479FF;
        margin-right: 10px;
      }
      .stu-len {
        margin-left: 20px;
        margin-right: 50px;
        font-weight: 500;
        font-size: 16px;
        color: #3479FF;
      }
    }
    .drawer-detail {
      margin-top: 20px;
      width: 100%;
      height: calc(100% - 60px);
      overflow-y: auto;

      .stu-item {
        padding: 20px 20px;
        box-sizing: border-box;
        font-size: 18px;
        color: #19191A;
        display: inline-block;
      }
    }
}
.code-share {
  font-size: 16px;
  padding: 10px 0;
  color: #000000;
}
.code-share-title {
  font-size: 18px;
  padding: 10px 0;
  color: #000000;
  font-weight: 600;
}
</style>

<style lang='scss'>
.grade-drawer {
  width: 100%;
  height: 100%;
}
</style>
