<template>
  <NormalDialog
    v-if="dialogShow"
    width="800px"
    :title="title"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="flex flex-col editor-dig w">
      <div class="mb10">
        <el-input
          v-model="content"
          :rows="3"
          type="textarea"
          class="w"
          placeholder="请输入标注内容"
        />
      </div>
      <div class="mb10">
        <div
          ref="content"
          style="max-width: 100%;"
          class="w"
          v-html="keyword"
        ></div>
      </div>
    </div>
    <template #footer>
      <div class="edu-btn" @click="onSubmit">确定</div>
    </template>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'

export default {
  components: { NormalDialog },

  data() {
    return {
      cbs: null,
      dialogShow: false,
      appendToBody: false,
      title: '添加标注',
      keyword: '',
      content: ''
    }
  },
  mounted() {
    this.dialogShow = true
  },
  methods: {
    close() {
      this.dialogShow = false
      this.keyword = ''
      this.content = ''
      document.getElementsByClassName('tox-tinymce-aux')[0].style.display = 'block'
    },
    open(cbs = {}) {
      this.dialogShow = true
      this.cbs = cbs
      this.$nextTick(() => {
        window.MathJax.typesetPromise([this.$refs.content])
      })
    },
    onSubmit() {
      if (
        Object.prototype.toString.call(this.cbs['onSubmit']) ===
        '[object Function]'
      ) {
        this.cbs['onSubmit']({
          keyword: this.keyword,
          content: this.content
        })
        this.keyword = ''
        this.content = ''
      }
    },
    extractFormula(content) {
      const parser = new DOMParser()
      const doc = parser.parseFromString(content, 'text/html')
      const mathElement = doc.querySelector('.math-tex')
      if (mathElement && mathElement.dataset.latex) {
        // 提取 data-latex 中的内容，并确保使用正确的分隔符
        const formula = mathElement.dataset.latex.replace(/^\\\(|\\\)$/g, '')
        return `<span class="math-tex">\\(${formula}\\)</span>`
      }
      return content
    },

    setData(data) {
      this.keyword = this.extractFormula(data.keyword)
      this.content = data.content
      this.$nextTick(() => {
        window.MathJax.typesetPromise([this.$refs.content]).catch((err) => {
          console.error('MathJax typeset error:', err)
        })
      })
    },
    onCancel() {
      if (
        Object.prototype.toString.call(this.cbs['onCancel']) ===
        '[object Function]'
      ) {
        this.cbs['onCancel']()
      }
    }
  }
}
</script>

  <style lang="scss" scoped>
.editor-dig {
  ::v-deep .el-input__inner {
    transition: none;
  }
  .school-disc {
    margin-top: 10px;
  }

  .mb10 {
    margin-bottom: 10px;
  }

  .school-disc2 {
    width: 5px;
    height: 5px;
    background: #828282;
    border-radius: 50%;
    margin-right: 5px;
  }
}

::v-deep th {
  border: 1px solid #bbb;
}
::v-deep td {
  border: 1px solid #bbb;
}
::v-deep table {
  border-collapse: collapse;
}
</style>
