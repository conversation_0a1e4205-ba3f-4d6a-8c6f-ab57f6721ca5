<template>
  <NormalDialog
                v-if="dialogShow"
                width="80vw"
                :defaultHeader="false"
                :dialog-visible="dialogShow"
                :is-center="true"
                :append-to-body="true"
                @closeDialog="close">
    <template #header>
      <div class='header_view'>
        <div>邀请使用管理</div>
        <div class='sub_title'>
          <span>邀请剩余：{{otherList.length}}/{{list.length}}</span>
        </div>
      </div>
    </template>
    <div class='main_view'>
      <Empty v-if='list.length === 0' :msg="'暂无数据'" />
      <div class='list_item' v-for='item in list' :key='item.id'>
        <div class='avatar_view'>
          <template v-if='item.user'>
            <el-avatar :size="40" src="https://empty">
              <img :src="item.user.avatar"/>
            </el-avatar>
            <div style='margin-left: 20px'>
              <div>{{item.user.displayName}}</div>
            </div>
          </template>
          <div v-else>
            兑换人：--
          </div>
        </div>
        <div class='phone_view'>
          <span v-if='item.user'>{{item.user.mobile}}</span>
          <span v-else>电话：--</span>
        </div>
        <div class='status_view'>
          <span v-if='item.user'>已兑换</span>
          <template v-else>
            <span v-if='item.inviteStatus === "YES"'>已邀请（未兑换）</span>
            <span v-if='item.inviteStatus === "NOT"'>未邀请</span>
          </template>
        </div>
        <div class='code_view'>
          <span>兑换码：{{item.coverCode}}</span>
        </div>
        <div class='remark_view'>
          <div class='remark_title'>备注：</div>
          <div class='remark_content' v-if="item.inviteNote && item.inviteNote !== ''">{{item.inviteNote}}</div>
          <div v-else>--</div>
        </div>
        <div class='option_view'>
          <span v-if='item.user' class='time'>
            兑换时间：{{formatDateTime(item.updatedAt)}}
          </span>
          <span v-else class='pointer' @click='handleCopy(item)'>复制邀请链接</span>
        </div>
      </div>
    </div>
    <NormalDialog v-if="remarkShow"
                  width="500px"
                  title="邀请备注"
                  :dialog-visible="remarkShow"
                  :is-center="true"
                  :append-to-body="true"
                  @closeDialog="closeRemark">
      <div class='remark_dialog_content'>
        <el-input v-model='inviteNote' placeholder='请输入邀请备注' type='textarea' :rows='5' style='width:100%'></el-input>
      </div>
      <template #footer>
        <div class='bottom_btn_view'>
          <el-button type="primary" size="small" @click='submit'>确定</el-button>
        </div>
      </template>
    </NormalDialog>
  </NormalDialog>

</template>

<script>
import { formatDateTime } from '@/utils/date'
import { getInvitationList, updateEmpowerExchangeCode } from '@/api/cloudLecture-api'
import NormalDialog from '@/components/classPro/NormalDialog/index2'
import Empty from '@/components/classPro/Empty/index.vue'
export default {
  components: { NormalDialog, Empty },
  props: {
    bookId: {
      type: Number,
      default: () => 0
    },
    bookTitle: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      list: [],
      dialogShow: false,
      formatDateTime,
      remarkShow: false,
      inviteNote: '',
      currentNode: null,
      id: this.bookId,
      title: this.bookTitle
    }
  },
  methods: {
    close() {
      this.dialogShow = false
    },
    show(id = null, title = null) {
      this.dialogShow = true
      if (id) {
        this.id = id
      } else {
        this.id = this.bookId
      }
      if (title) {
        this.title = title
      } else  {
        this.title = this.bookTitle
      }
      this.getList()
    },
    async getList() {
      const { data } = await getInvitationList({
        digitalBookId: this.id
      })
      this.list = data.map(item => {
        return {
          ...item,
          coverCode: item.code.substring(0, 3) + '*'.repeat(item.code.length - 3)
        }
      })
    },
    closeRemark() {
      this.remarkShow = false
    },
    handleCopy(data) {
      this.currentNode = data
      this.inviteNote = data.inviteNote
      this.remarkShow = true
    },
    async submit() {
      try {
        await updateEmpowerExchangeCode({
          id: this.currentNode.id,
          inviteNote: this.inviteNote,
          inviteStatus: 'YES'
        })
        this.remarkShow = false
        let text = `邀请您使用云讲义：${this.title}讲义
客户端下载地址：https://bingobook.cn/download
兑换码：${this.currentNode.code}

使用步骤：
①注册并登录
②点击右上角兑换输入兑换码
③兑换后选择左侧菜单【我的教材】并查看使用`
        await this.copyToClipboard(text)
        await this.getList()
      } catch (e) {
        console.log(e)
      }
    },
    copyToClipboard(text) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
          this.$message.success('复制成功，快去粘贴吧！')
        }).catch(err => {
          console.error('复制失败', err)
        })
      }
    }
  },
  computed: {
    otherList() {
      return this.list.filter(item => item.inviteStatus === 'NOT' && !item.user)
    }
  }
}
</script>

<style scoped lang='scss'>
::v-deep .bingo-normal-dialog2 .dialog-box{
  padding: 0;
}
.header_view{
  width: 100%;
  height: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 40px;
  font-weight: 600;
  font-size: var(--font-size-L);
  .sub_title{
    font-weight: 400;
    font-size: 12px;
  }
}
.main_view{
  width: 100%;
  position: relative;
  padding: 0 10px;
  height: 60vh;
  overflow: auto;

  ::v-deep .empty {
    width: 80px !important;
    height: 80px !important;
  }

  .list_item{
    width: 100%;
    height: 60px;
    display: flex;
    align-items: center;
    font-size: 16px;
    border-bottom: 1px solid rgba(242, 242, 242, 1);
    .avatar_view{
      width: 15%;
      height: 100%;
      color: rgba(0, 0, 0, 1);
      font-weight: 500;
      display: flex;
      align-items: center;
      font-size: var(--font-size-L);
      overflow-x: hidden;
      text-overflow: ellipsis;
    }
    .phone_view{
      width: 15%;
      height: 100%;
      color: rgba(130, 130, 130, 1);
      font-size: var(--font-size-L);
      display: flex;
      align-items: center;
    }

    .status_view{
      width: 15%;
      height: 100%;
      color: rgba(79, 79, 79, 1);
      display: flex;
      align-items: center;
      font-size: var(--font-size-L);
    }
    .code_view{
      width: 15%;
      height: 100%;
      color: rgba(79, 79, 79, 1);
      display: flex;
      align-items: center;
      font-size: var(--font-size-L);
    }
    .remark_view{
      width: 20%;
      height: 100%;
      color: rgba(79, 79, 79, 1);
      display: flex;
      align-items: center;
      font-size: var(--font-size-L);
      .remark_title{
        width: 40px;
        height: 100%;
        display: flex;
        align-items: center;
      }
      .remark_content{
        width: calc(100% - 40px);
        height: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: flex;
        align-items: center;
      }
    }
    .option_view{
      width: 20%;
      height: 100%;
      color: rgba(47, 128, 237, 1);
      display: flex;
      align-items: center;
      font-size: var(--font-size-L);
      .time{
        font-size: var(--font-size-M);
        color: rgba(79, 79, 79, 1);
      }
    }
  }
}
.remark_dialog_content{
  width: 100%;
}
.bottom_btn_view{
  width: 100%;
  display: flex;
  justify-content: center;
}
</style>
