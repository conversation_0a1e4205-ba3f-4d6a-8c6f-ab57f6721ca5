<template>
  <NormalDialog v-if="dialogShow"
                width="40vw"
                :title="type === 'edit' ? '课时信息' : '创建课时'"
                :dialog-visible="dialogShow"
                :is-center="true"
                :append-to-body="true"
                :dig-class="true"
                @closeDialog="close">
    <div class='main' style='width:100%'>
      <el-form ref='form' :model='info' label-width='8vw' :rules='rules' label-position='right' size="mini">
        <el-form-item label='课时标题' prop='title'>
          <el-input v-model='info.title' placeholder='请输入课时标题' style='width:100%'/>
        </el-form-item>
        <el-form-item label='封面'>
          <el-upload
            class="avatar-uploader"
            action=""
            :show-file-list="false"
            accept='.png, .jpg, .jpeg'
            :before-upload="beforeAvatarUpload"
          >
            <el-image fit="cover"  v-if="info.cover" :src="ossUrl+info.cover" :key='imageKey' class="avatar" />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            <div class="el-upload__tip" slot="tip" style='display: flex; justify-content: space-between'>
              <span>支持png、jpg、jpeg格式图片，宽高比4:3</span>
              <div class='ai_btn' @click='toAIView'>AI生成封面</div>
            </div>
          </el-upload>
          <el-button :disabled='!info.cover || info.cover === ""' style='position: absolute;top: 0;right: 0' type='primary' size='mini' @click='setImage'>图片设置</el-button>
        </el-form-item>
        <el-form-item label='课时简介'>
          <el-input v-model='info.subtitle' placeholder='输入课时简介，一句话介绍，不超过50字' style='width:100%' type="textarea" :rows="3" maxlength='50'/>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class='bottom_btn_view'>
        <el-button size="small" @click="close">取消</el-button>
        <el-button type="primary" size="small" @click='submit'>确定</el-button>
      </div>
    </template>
    <AICoverDialog ref='AICoverDialog' @use='userAICover'/>
  </NormalDialog>
</template>

<script>
import AICoverDialog from '@/views/publishingReview/author/components/AICoverDialog'
import NormalDialog from '@/components/classPro/NormalDialog/index2'
import { getFileUploadAuthor } from '@/api/user-api'
import axios from 'axios'
import { editBookCatalogue } from '@/api/digital-api'
import { getToken } from 'utils/auth'
import { addSetImgSizeModal, closeSetImgSize, setData } from '@/views/digitalbooks/editor/components/setImgSize'

export default {
  components: { NormalDialog, AICoverDialog },
  props: {
    bookId: {
      type: Number,
      default: () => 0
    },
    parentId: {
      type: Number,
      default: () => 0
    }
  },
  data() {
    return {
      dialogShow: false,
      ossUrl: '',
      type: 'add',
      isAdmin: false,
      imageKey: 0,
      info: {
        title: '',
        cover: '',
        subtitle: ''
      },
      rules: {
        title: [{
          required: true,
          trigger: 'blur',
          message: '请输入课时标题'
        }],
        cover: [{
          required: true,
          message: '请上传封面'
        }]
      },
      token: ''
    }
  },
  mounted() {
    this.token = getToken()
  },
  methods: {
    close() {
      this.info = { title: '', cover: '', subtitle: '' }
      this.ossUrl = ''
      this.dialogShow = false
      this.isAdmin = false
      this.type = 'add'
    },
    show(data = null) {
      if (data) {
        this.info = JSON.parse(JSON.stringify(data))
        this.type = 'edit'
        this.info.cover = this.info.background
      }
      this.dialogShow = true
    },
    async beforeAvatarUpload(file) {
      const _this = this
      const isLt2M = file.size / 1024 / 1024 < 1
      if (!isLt2M) {
        this.$message.warning('上传图片超过1M,可能会影响体验，请压缩图片')
      }
      const { data } = await getFileUploadAuthor({
        mediaType: 'IMAGE',
        contentType: '',
        quantity: 1,
        fileName: file.name
      })
      this.ossUrl = data[0].ossConfig.host
      try {
        this.progress = true
        const formData = new FormData()
        formData.append('success_action_status', '200')
        formData.append('callback', '')
        formData.append('key', data[0].fileName)
        formData.append('policy', data[0].policy)
        formData.append('OSSAccessKeyId', data[0].ossConfig.accessKeyId)
        formData.append('signature', data[0].signature)
        formData.append('file', file)

        await axios.post(this.ossUrl, formData, {
          onUploadProgress: (progress) => {
            const complete = Math.floor(progress.loaded / progress.total * 100)
            this.percent = complete
            if (complete >= 100) {
              this.progress = false
              this.percent = 0
            }
          }
        })
        if (!isLt2M) {
          setData({
            url: `${this.ossUrl}/${data[0].fileName}`
          })
          addSetImgSizeModal({
            onSubmit (data) {
              _this.ossUrl = ''
              _this.info.cover = `${data.url}`
              closeSetImgSize()
            },
            onCancel() {
              // console.log(`oss: ${_this.ossUrl}`)
              // console.log(`url: ${_this.info.cover}`)
              if (_this.info.cover.includes('https://')) {
                _this.ossUrl = ''
              }
            }
          })
        } else {
          this.info.cover = `/${data[0].fileName}`
        }
        this.imageKey++
      } catch (e) {
        console.log(e)
      }
      return false
    },
    setImage() {
      const _this = this
      setData({
        url: this.ossUrl + this.info.cover
      })
      addSetImgSizeModal({
        onSubmit (data) {
          _this.ossUrl = ''
          _this.info.cover = `${data.url}`
          closeSetImgSize()
        },
      })
    },
    toAIView() {
      this.$refs.AICoverDialog.show()
    },
    userAICover(url, ossUrl, fileName) {
      this.ossUrl = ossUrl
      this.info.cover = fileName
      this.imageKey++
    },
    submit() {
      this.$refs.form.validate(async valid => {
        if (valid) {
          const form = {
            bookId: this.bookId,
            parentId: this.parentId,
            title: this.info.title,
            type: 'DIGITAL_CLOUD_LECTURE_COURSE',
            subtitle: this.info.subtitle,
            background: this.info.cover
          }
          if (this.type === 'edit') {
            form.id = this.info.id
          }
          const { code } = await editBookCatalogue(form, { authorization: this.token })
          if (code === 200) {
            if (this.type === 'edit') {
              this.$message.success('修改成功')
            } else {
              this.$message.success('创建成功')
            }
            this.info = { title: '', cover: '', subtitle: '' }
            this.type = 'add'
            this.$emit('addSuccess')
            this.close()
          }
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style scoped lang='scss'>
::v-deep .el-form-item__content {
  font-size: 12px !important;

}
::v-deep .el-form-item{
  margin-bottom: 18px !important;
}

.tip_view{
  font-size: 10px;
  color: #828282;
}
::v-deep .avatar-uploader .el-upload {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 160px;
  height: 120px;
  line-height: 120px;
  text-align: center;
}
.avatar {
  width: 160px;
  height: 120px;
  display: block;
  object-fit: cover;
}
.ai_btn{
  padding-right: 10px;
  padding-left: 10px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 3px 3px 3px 3px;
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #fff;
  background: linear-gradient(to right, rgba(31, 162, 255, 1), rgba(18, 216, 250, 1), rgba(166, 255, 203, 1));
  &:hover{
    cursor: pointer;
  }
}
.bottom_btn_view{
  height: 40px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

</style>
