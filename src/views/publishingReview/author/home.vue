<template>
  <div class="main">
    <div class="header">
      <img class="icon" src="../../../assets/publishingReview/author.png" alt="" />
      <div class="avatar"><img
        :src="avatar ? avatar : require('../../../assets/publishingReview/default_avator.png')"
        alt=""
      /></div>
      <div class="name" :title="name">{{ name }}</div>
      <el-dropdown class="setting">
        <span class="el-dropdown-link">
          设置<i class="el-icon-arrow-down el-icon--right"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item @click.native="dialogInfoVisible = true">修改头像</el-dropdown-item>
          <el-dropdown-item @click.native="openUserName">用户名</el-dropdown-item>
          <el-dropdown-item @click.native="dialogVisible = true">设置密码</el-dropdown-item>
          <el-dropdown-item @click.native="logout">退出登录</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <div class="content">
      <div class="left">
        <div class="item" :class="tabIndex === 0 ? 'active' : ''">
          <svg
            class="svg_item"
            width="26"
            height="26"
            viewBox="0 0 26 26"
            :fill="tabIndex === 0 ? '#2F80ED' : '#4F4F4F'"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M6.18475 6.42103C7.66913 6.42103 9.88923 6.60173 12.0964 8.37007L12.0706 17.4699C10.8186 17.0182 8.83081 16.5406 7.04956 16.5406C6.67524 16.5406 6.31383 16.5664 5.96532 16.6051V6.42103H6.18475ZM6.18475 5.14317C5.2554 5.14317 4.67456 5.25934 4.67456 5.25934V18.2186C5.43611 17.9475 6.24929 17.8314 7.04956 17.8314C10.1087 17.8314 13.0774 19.419 13.0774 19.419V7.53108C10.6121 5.50459 7.84983 5.14317 6.18475 5.14317ZM19.8668 6.27904H20.2153V16.4373C19.6087 16.2824 18.9762 16.205 18.3437 16.205C16.8077 16.205 14.7554 16.7342 13.697 17.2247V8.47334C16.1107 6.27904 18.3953 6.27904 19.8668 6.27904ZM19.8668 4.98828C18.1888 4.98828 15.4782 5.38842 13.0903 7.53108V19.419C13.0903 19.419 15.4137 17.4958 18.3308 17.4958C19.3376 17.4958 20.4089 17.7281 21.4932 18.3347V5.13026C21.4932 5.13026 20.8607 4.98828 19.8668 4.98828Z"
              :fill="tabIndex === 0 ? '#2F80ED' : '#4F4F4F'"
            />
            <path
              d="M12.8709 21.0117C12.7934 21.0117 12.716 20.9988 12.6514 20.973C7.54002 19.0498 3.6161 20.689 3.57738 20.7148C3.38376 20.8052 3.15143 20.7794 2.97072 20.6632C2.87882 20.6029 2.80351 20.5204 2.75165 20.4235C2.69979 20.3265 2.67304 20.2181 2.67385 20.1082V6.81335C2.67385 6.46484 2.95781 6.16797 3.31923 6.16797C3.68064 6.16797 3.96461 6.45194 3.96461 6.81335V19.2176C5.4877 18.7916 8.79205 18.2108 12.8321 19.6564C13.9164 19.1014 18.0339 17.2685 22.0482 18.8045V6.81335C22.0482 6.46484 22.3321 6.16797 22.6936 6.16797C23.0421 6.16797 23.3389 6.45194 23.3389 6.81335V19.7984C23.3389 20.0178 23.2228 20.2244 23.0291 20.3534C22.8355 20.4696 22.6032 20.4825 22.3967 20.3663C18.1888 18.1591 13.2452 20.8956 13.2065 20.9214C13.0903 20.973 12.987 21.0117 12.8709 21.0117Z"
              :fill="tabIndex === 0 ? '#2F80ED' : '#4F4F4F'"
            />
          </svg>
          <div class="title" @click="changeType(0)">
            我的教材
          </div>
        </div>
        <div class="item" :class="tabIndex === 1 ? 'active' : ''">
          <svg class="svg_item" width="26" height="25" viewBox="0 0 26 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M8.83325 22.9193H3.62492C3.34865 22.9193 3.0837 22.8095 2.88835 22.6142C2.693 22.4188 2.58325 22.1539 2.58325 21.8776V9.89844C2.58325 9.62217 2.693 9.35722 2.88835 9.16187C3.0837 8.96652 3.34865 8.85677 3.62492 8.85677H8.83325V3.1276C8.83325 2.85134 8.943 2.58638 9.13835 2.39103C9.3337 2.19568 9.59865 2.08594 9.87492 2.08594H16.1249C16.4012 2.08594 16.6661 2.19568 16.8615 2.39103C17.0568 2.58638 17.1666 2.85134 17.1666 3.1276V13.0234H22.3749C22.6377 13.0234 22.8908 13.1226 23.0835 13.3013C23.2762 13.48 23.3943 13.7249 23.414 13.987L23.4166 14.0651V21.8776C23.4167 22.1404 23.3174 22.3935 23.1387 22.5862C22.96 22.7789 22.7151 22.897 22.453 22.9167L22.3749 22.9193H8.83325ZM8.83325 10.4193H4.14575V21.3568H8.83325V10.4193ZM21.8541 14.5859H17.1666V21.3568H21.8541V14.5859ZM15.6041 3.64844H10.3958V21.3568H15.6041V3.64844Z" :fill="tabIndex === 1 ? '#2F80ED' : '#4F4F4F'" />
          </svg>
          <div class="title" @click="changeType(1)">
            使用统计
          </div>
          <div v-if="idotReview !== 0" class="idot">{{ idotReview }}</div>
        </div>
      </div>
      <div v-if="tabIndex===1" class="right">
        <div v-if="authorDataList.length === 0" class="w" style="height: 100%">
          <Empty :msg="'暂无数据'" style="transform: scale(0.7);" />
        </div>
        <div v-else class="book_content" style="margin-top: 0;height: 100%;">
          <div ref="lineChart" class="chart">
            <div v-if="!chartData" class="w" style="height: 100%">
              <Empty :msg="'暂无数据'" style="transform: scale(0.7);" />
            </div>
          </div>
          <div class="person">累计使用人数：<span>{{ getAllUser() }}</span>人</div>
          <div class="bookInfoList">
            <p v-for="(item,index) in authorDataList" :key="index">
              {{ item.title }}：<span>{{ item.useData.useQuantity }}</span>人
            </p>
          </div>
        </div>
      </div>
      <div v-if="tabIndex===0" class="right">
        <div class="right_header">
          <p>我的教材</p>
          <el-button type="primary" class="header_button" @click="openToast">创建教材</el-button>
        </div>
        <div v-if="dataList.length === 0" class="w" style="height: 100%">
          <Empty :msg="'暂无数据'" style="transform: scale(0.7);" />
        </div>
        <div v-else class="book_content">
          <div v-for="(item, index) in dataList" :key="index" class="book_item">
            <el-tag v-if="!item.reviewStatusPublisher && !item.reviewStatusExpert" class="tag" color="#FFF1E5" style="color:#F2994A">未提交</el-tag>
            <el-tag v-if="item.reviewStatusPublisher==='FAILED' || item.reviewStatusExpert==='FAILED'" color="#FFE7E7" style="color:#EB5757" class="tag">已返修</el-tag>
            <p v-if="item.reviewStatusPublisher==='FAILED' || item.reviewStatusExpert==='FAILED'" class="faild_to_edit" @click="toEdit(item)">查看</p>
            <el-tag v-if="item.reviewStatusPublisher==='PASS'" class="tag" color="#EBF4FF" style="color:#2D9CDB">已出版</el-tag>
            <el-tag v-if="item.reviewStatusPublisher==='UNDER_REVIEW' || item.reviewStatusExpert==='UNDER_REVIEW'" color="#C3FFDD" style="color:#27AE60" class="tag">审核中</el-tag>
            <img class="post" :src="item.digitalBook.cover" alt="" />
            <div class="title">{{ item.digitalBook.title }}</div>
            <div class="info">
              <p v-if="item.digitalBook.publisher">
                出版社：{{ item.digitalBook.publisher }}
              </p>
              <p v-if="item.digitalBook.edition">
                版次：{{ item.digitalBook.edition }}
              </p>
              <p v-if="item.digitalBook.isbn">
                ISBN：{{ item.digitalBook.isbn }}
              </p>
            </div>
            <div class="button_group">
              <el-button type="primary" plain @click="toEdit(item)">教材内容</el-button>
              <el-button type='primary' plain @click='toLectureNotes(item)'>智能云讲义</el-button>
              <el-button type="primary" plain @click="edit(item)">教材信息</el-button>
              <el-popover
                placement="bottom-start"
                trigger="click"
                popper-class="custom-popover">
                <div class='popover_btn_view'>
                  <el-button class='popover_btn' type="primary" @click="goTask(item)">任务工单</el-button>
                  <el-button class='popover_btn' type="primary" @click="openKnowledgeToast(item)">知识图谱</el-button>
                  <el-button class='popover_btn' type="primary" @click="toClass(item)">教材讲义</el-button>
                  <el-button class='popover_btn' type="primary" @click="toResource(item)">资源库</el-button>
                  <el-button class='popover_btn' type="primary" @click="noOnline(item)">教学实训</el-button>
                  <el-button class='popover_btn' type="primary" @click="openeEditionToast(item)">出版详情</el-button>
                  <el-button class='popover_btn' v-if="item.isAdmin" type="danger" :disabled="item.reviewStatusPublisher==='PASS'" @click="deleteBook(item)">删除教材</el-button>
                </div>
                <el-button slot="reference" type="primary" plain style='margin-left: 12px'>更多<i class="el-icon-arrow-right"></i></el-button>
              </el-popover>
            </div>
            <div v-for="(item1,index1) in item.digitalBook.authorUserList.slice(0,4)" :key="index1" class="author" :style="{right:1.3*(index1+1) +'vw', zIndex:999-index1}">
              <img class="img" :src="item1.user.avatar?item1.user.avatar:require('../../../assets/publishingReview/default_avator.png')" alt="" />
              <span v-if="item.digitalBook.authorUserList.length>=4&&index1===0" style="position: absolute;bottom:0;right: -15px;">...</span>
            </div>
            <img class="invite_icon" src="../../../assets/publishingReview/invite.png" alt="" @click="openQrCode(item)" />
            <el-button class="invite_author" type="text" :style="{right:5*1.8+'vw'}" @click="openQrCode(item)">邀请主编</el-button>
          </div>
        </div>
      </div>
    </div>
    <NormalDialog title="修改密码" :dialog-visible="dialogVisible" width="30%" @closeDialog="dialogVisible=false">
      <!-- 表单部分 -->
      <el-form ref="form" style="width: 100%;" :model="form" label-width="6vw">
        <el-form-item label="原密码">
          <el-input v-model="form.oldPassword" placeholder="请输入密码，如未设置可不输入" type="password" show-password />
        </el-form-item>
        <el-form-item label="新密码">
          <el-input v-model="form.newPassword" placeholder="请输入新密码" type="password" show-password />
        </el-form-item>
        <el-form-item label="确认密码">
          <el-input v-model="form.confirmPassword" placeholder="请确认新密码" type="password" show-password />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </span>
    </NormalDialog>
    <NormalDialog title="修改头像" :dialog-visible="dialogInfoVisible" width="30%" @closeDialog="dialogInfoVisible=false">
      <!-- 表单部分 -->
      <div class="row1">
        <el-upload
          class="avatar-container"
          :action="uploadUrl"
          :show-file-list="false"
          :headers="handleHeader"
          :accept="handleAccept"
          :on-error="handleError"
          :on-progress="handleProgress"
          :on-success="handleAvatarSuccess"
          :before-upload="beforeAvatarUpload"
        >
          <img :src="avatar?avatar:require('../../../assets/publishingReview/default_avator.png')" class="avatar" />
          <img class="edit-avatar" src="../../../assets/images/dashboard/editAvatar.png" alt="" />
        </el-upload>
      </div>
    </NormalDialog>
    <NormalDialog title="修改昵称" :dialog-visible="dialogNameVisible" width="30%" @closeDialog="dialogNameVisible=false">
      <el-form ref="form" :model="form" label-width="3vw" style="width: 100%;">
        <el-form-item label="昵称">
          <el-input v-model="userName" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogNameVisible=false">取消</el-button>
        <el-button type="primary" @click="uptadeUserInfo()">确定</el-button>
      </template>
    </NormalDialog>
    <addBookToast ref="addBookToast" @addSuccess="getData" />
    <knowledgeToast ref="knowledgeToast" :url="knowledgeUrl" />
    <editionPop ref="editionToast" @read="openRead" />
    <div v-if="preShow" class="pre">
      <Read :pre-mode="true" :book-id-props="preBookId" :pre-publish-mode="true" :pre-edition-id="preEditionId" @close="preShow = false" />
    </div>
    <QrCode ref="qrcode" />
  </div>
</template>
<script>
import { getDigitalBookDataAuthor, getDigitalBookReviewList, digitalBook } from '@/api/publishing'
import Empty from '@/components/classPro/Empty/index.vue'
import { mapGetters } from 'vuex'
import { updatePassword } from '@/api/user-api'
import addBookToast from './components/addBookToast.vue'
import { getAuthorToken } from '@/utils/auth'
import knowledgeToast from './components/knowledgeToast.vue'
import editionPop from './components/editionPop.vue'
import Read from '@/views/digitalbooks/read/index.vue'
import * as echarts from 'echarts'
import NormalDialog from '@/components/classPro/NormalDialog/index2.vue'
import { updateUserInfo } from '@/api/user-api'
import QrCode from './components/QrCode.vue'
export default {
  components: { Empty, addBookToast, knowledgeToast, editionPop, Read, NormalDialog, QrCode },
  data() {
    return {
      dialogVisible: false,
      dialogNameVisible: false,
      dialogInfoVisible: false,
      dialogShow: false,
      publishInfo: null,
      preBookId: '0',
      preShow: false,
      tabIndex: 0,
      imgSrc: '',
      preNode: null,
      prePublishMode: false,
      popImg: '',
      type: 0,
      idotReview: 0,
      userName: '',
      userNum: 0,
      idotFiald: 0,
      chartData: [],
      isAdmin: false,
      dataList: [
      ],
      authorDataList: [],
      form: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      knowledgeUrl: '',
      handleHeader: {
        Authorization: getAuthorToken()
      },
      handleAccept: 'image/*',
      preEditionId: '0'
    }
  },
  computed: {
    ...mapGetters([
      'name',
      'avatar',
      'id'
    ]),
    uploadUrl: function () {
      return `${process.env.VUE_APP_BASE_API}/api/v1/students/avatar`
    }
  },
  mounted() {
    document.title = this.$route.meta.title
    this.getData()
    // this.$nextTick(() => {
    //   this.$store.dispatch('user/GetInfo')
    // })
  },
  methods: {
    toResource(item) {
      this.$router.push({
        path: '/author/resources',
        query: { id: item.digitalBook.id, type: 'DIGITAL_RESOURCE_OFFICIAL', token: getAuthorToken() }
      })
    },
    toEdit(item) {
      this.$router.push({ path: '/editor', query: { id: item.digitalBook.id, token: getAuthorToken(), uuid: this.$route.query.uuid ? this.$route.query.uuid : null }})
    },
    toLectureNotes(item) {
      this.$router.push({ path: '/author/lectureNotes', query: { path: '/author/home', bookId: item.digitalBook.id, token: getAuthorToken() }})
    },
    toClass(item) {
      this.$router.push({ path: '/author/class', query: { bookId: item.digitalBook.id, token: getAuthorToken() }})
    },
    goTask(item) {
      this.$router.push({ path: '/author/task', query: { token: getAuthorToken(), bookId: item.digitalBook.id }})
    },
    noOnline() {
      this.$message.info('即将上线，敬请期待')
    },
    openQrCode(item) {
      const bookData = { ...item.digitalBook }
      this.$refs.qrcode.show(bookData)
    },
    uptadeUserInfo() {
      updateUserInfo({ displayName: this.userName }).then(res => {
        if (res.code === 200) {
          this.$store.dispatch('user/EditName', this.userName)
          this.dialogNameVisible = false
          this.$message.success('修改成功')
        }
      })
    },
    openUserName() {
      this.dialogNameVisible = true
      this.userName = this.name
    },
    handleProgress () {
      this.uploading = true
    },
    handleError (e) {
      this.$message.error(e)
      this.uploading = false
    },
    handleAvatarSuccess (response, file) {
      this.uploading = false
      if (response.code === '200') {
        this.$store.dispatch('user/GetInfo')
        this.$message.success('修改成功')
        this.dialogInfoVisible = false
      } else {
        this.$message.error(response.message)
      }
    },
    beforeAvatarUpload (file) {
      const isLt2M = file.size / 1024 / 1024 < 10
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 10MB!')
        this.uploading = false
      }
      return isLt2M
    },
    openRead(data) {
      this.preBookId = String(data.bookId)
      this.preEditionId = String(data.editionId)
      this.preShow = true
    },
    openeEditionToast(item) {
      this.$refs.editionToast.show(item.digitalBook.id)
    },
    edit(data) {
      const bookData = { ...data.digitalBook }
      this.$refs.addBookToast.show(bookData)
    },
    getAllUser() {
      let num = 0
      this.authorDataList.forEach(item => {
        num += item.useData.useQuantity
      })
      return num
    },
    initLineChart() {
      if (!this.chartData) return
      const chartDom = this.$refs.lineChart
      const myChart = echarts.init(chartDom)
      const yData = []
      const xData = []
      if (this.chartData) {
        this.chartData.forEach((item, index) => {
          xData.push(Object.keys(item)[0])
          yData.push(item[Object.keys(item)[0]])
        })
      }
      const option = {
        title: {
          text: '用户增长曲线'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: xData
        },
        yAxis: {
          type: 'value',
          minInterval: 1,
          show: true,
          axisLine: {
            show: true,
            symbol: ['none', 'arrow'],
            symbolSize: [8, 8],
            symbolOffset: [0, 7]
          }
        },
        series: [{
          data: yData,
          type: 'line'
        }]
      }
      myChart.setOption(option)
    },
    openKnowledgeToast(item) {
      this.knowledgeUrl = process.env.VUE_APP_ADMIN_API + `/Home/DigitalBook/graphIndex?Authorization=${getAuthorToken()}&book_id=${item.digitalBook.id}`
      // window.open(this.knowledgeUrl, '_blank')
      this.$nextTick(() => {
        this.$refs.knowledgeToast.show()
      })
    },
    deleteBook(item) {
      this.$confirm('是否要删除教材?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { code } = await digitalBook({ id: item.digitalBook.id, apiType: 'delete' })
        if (code === 200) {
          this.$message.success('删除成功')
          this.getData()
        }
      })
    },
    logout() {
      this.$confirm('是否要退出登录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('user/AuthorLogout')
        this.$router.push('/author/login')
        // location.reload()
      })
    },
    openToast() {
      this.$refs.addBookToast.show()
    },
    async getData() {
      getDigitalBookReviewList().then(async res => {
        const scoreArr = res.data ? res.data : []
        for (let i = 0; i < scoreArr.length; i++) {
          const admin = scoreArr[i].digitalBook.authorUserList.find(item => item.user.id === this.id && item.tag === 'admin')
          if (admin) {
            scoreArr[i].isAdmin = true
          }
          scoreArr[i].authorList = scoreArr
        }

        this.dataList = scoreArr
      })
    },
    getAuthorData() {
      getDigitalBookDataAuthor().then(res => {
        this.authorDataList = res.data ? res.data.digitalBookList : []
        this.$nextTick(() => {
          this.chartData = res.data ? res.data.dateDataMapList : []
          this.userNum = res.data.totalUseQuantity ? res.data.totalUseQuantity : 0
          this.initLineChart()
        })
      })
    },
    // 打开弹窗
    openDialog() {
      this.dialogVisible = true
    },
    // 提交表单
    submitForm() {
      // 这里应该添加表单验证和提交逻辑
      if (this.form.newPassword === '' || this.form.confirmPassword === '') {
        this.$message.error('新密码与确认密码不能为空')
        return
      }
      if (this.form.newPassword === this.form.confirmPassword) {
        updatePassword({
          passwordOld: this.form.oldPassword,
          passwordNew: this.form.newPassword
        }).then(res => {
          if (res.code === '200') {
            this.dialogVisible = false
          }
        })
      } else {
        // 密码不一致，给出提示
        this.$message.error('新密码与确认密码不一致')
      }
    },
    changeType(type) {
      this.tabIndex = type
      if (type === 0) {
        this.getData()
      } else if (type === 1) {
        this.getAuthorData()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__title{
  font-size: 12px;
}
::v-deep .el-form-item__label,
.el-input {
    font-size: var(--font-size-M);
    padding: 4px;
}

::v-deep .el-form-item {
    margin-bottom: 10px;
}

::v-deep .el-input__inner {
    height: 30px;
    padding: 5px;
}

::v-deep .el-button {
    padding: 6px;
    padding-top: 7px;
    font-weight: bold;
    font-size: var(--font-size-S);
}

::v-deep.el-dropdown-menu__item {
    // transform: scale(0.6);
    font-size: 8px;
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    text-align: center;
}

::v-deep .el-dropdown-menu__item {
    line-height: 2rem;
}

.el-dropdown-link {
    font-size: var(--font-size-M);
    color: #000;
}

.popover_btn_view{
  display: flex;
  flex-direction: column;
  .popover_btn{
    width: 100%;
    margin-left: 0px;
    margin-top: 4px;
    &.el-button--primary {
      background-color: #ffffff;
      border: 1px solid #e0e0e0;
      color: #333333;
      border-radius: 6px;

      &:hover {
        background-color: #f5f5f5;
        border-color: #d0d0d0;
        color: #333333;
      }

      &:focus {
        background-color: #ffffff;
        border-color: #e0e0e0;
        color: #333333;
      }
    }
  }
}

.pre {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 100%;
    z-index: 9000;
    overflow: auto;
}
.row1 {
    width: 100%;
    height: 97px;
    border-radius: 14px;
    .avatar-container {
      position: relative;
      width: 60px;
      height: 60px;
      cursor: pointer;
      margin: 0 auto;
      .avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
      }
      .edit-avatar {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 22px;
        height: 22px;
      }
    }}
.header {
    width: 100%;
    height: 50px;
    position: relative;

    .icon {
        width: auto;
        height: 20px;
        position: absolute;
        object-fit: cover;
        left: 30px;
        top: 20px;
    }

    .avatar {
        width: 24px;
        height: 24px;
        border-radius: 12px;
        background: #d8d4d4;
        position: absolute;
        top: 15px;
        left: 900px;
        cursor: pointer;
        img {
            width: 100%;
            height: 100%;
            border-radius: 12px;
            object-fit: cover;
        }
    }

    .name {
        position: absolute;
        top: 22px;
        left: 930px;
        width: 60px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: var(--font-size-M);
        cursor: pointer;
    }

    .setting {
        position: absolute;
        top: 22px;
        left: 1000px;
        font-size: var(--font-size-M);
        cursor: pointer;
    }
}

.content {
    width: 95%;
    height: calc(100vh - 80px);
    margin: 10px auto;
    display: flex;
    justify-content: space-between;
    position: relative;
    z-index: 1;

    .left {
        width: 15%;
        height: 100%;
        background: #ffffff;
        border-radius: 10px;
        padding: 10px;

        .item {
            width: 100%;
            height: 40px;
            position: relative;
            border-radius: 5px;
            margin-top: 10px;

            .svg_item {
                width: 18px;
                height: 18px;
                position: absolute;
                left: 30px;
                top: 10px
            }

            .idot {
                width: 12px;
                height: 12px;
                border-radius: 10px;
                color: #ffff;
                font-size: 8px;
                font-weight: 800;
                text-align: center;
                line-height: 12px;
                background: red;
                position: absolute;
                right: 30px;
                top: 13px
            }

            .title {
                width: 65px;
                height: 40px;
                line-height: 40px;
                margin: 0 auto;
                font-weight: 700;
                font-size: var(--font-size-L);
                text-align: left;
                cursor: pointer;
                box-sizing: border-box;
                color: #4F4F4F;
                padding-left: 15px;
                position: relative;
            }

        }

        .active {
            background: #E6F1FF;

            .title {
                color: #2f80ed;
            }
        }

        .item:nth-child(1) {
            margin-top: 20px;
        }
    }

    .right {
        width: 84%;
        height: 100%;
        background: #ffffff;
        border-radius: 10px;
        padding: 20px;
        overflow: auto;
        position: relative;
        @include scrollBar;
        .right_header {
            width: 100%;
            height: 30px;
            position: absolute;
            left: 0;
            top: 0;
            display: flex;
            justify-content: space-between;
            overflow: visible;
            padding-left: 10px;
            p {
                font-size: 17px;
                font-weight: 500;
                line-height: 30px;
                height: 30px;
                text-align: left;
                margin-left: 20px;

            }
            .header_button{
                font-size: 10px;
                margin-right: 20px;
                height: 25px;
                line-height: 10px;
                padding: 5px;
                margin-top: 20px;
            }
        }

        .book_content {
            width: 100%;
            height: 90%;
            overflow: auto;
            @include scrollBar;
            margin-top: 35px;
            position: relative;
            box-sizing: border-box !important;
            .chart{
              width: 100%;
              height: 70%;
              background: #E6F1FF;
              border-radius: 5px;
              padding: 20px;
              box-sizing: border-box !important;
            }
            .person{
              position: absolute;
              right: 20px;
              top:15px;
              font-size: 11px;
              font-weight: 500;
              span{
                font-size: 18px;
                color: #2F80ED;

              }
            }
            .bookInfoList{
              width: 100%;
              background: #E6F1FF;
              border-radius: 5px;
              margin-top: 10px;
              padding: 20px;
              padding-top: 0px;
              padding-bottom: 0px;
              box-sizing: border-box !important;
              overflow: auto;
              p{
                font-size: 11px;
              font-weight: 500;
              span{
                font-size: 18px;
                color: #2F80ED;

              }
              }
            }
        }
        .button {
            position: absolute;
            right: 90px;
            bottom: 20px;
            width: 60px;
        }

        .change_button {
            position: absolute;
            right: 90px;
            bottom: 20px;
            width: 80px;
        }

        .read_button {
            position: absolute;
            right: 20px;
            bottom: 20px;
            width: 60px;
        }
        .book_item {
            width: 100%;
            height: 170px;
            background: #FCFCFC;
            border-radius: 5px;
            position: relative;
            margin-top: 10px;
            .faild_to_edit{
              position: absolute;
              font-size: 12px;
              right:17px;
              top:35px;
              color:#EB5757;
              text-decoration: underline;
              cursor: pointer;
            }
            .tag{
              position: absolute;
              right: 10px;
              top:10px;
              font-size: var(--font-size-M);
              padding: 5px;
              height: 25px;
              line-height: 15px;
              border: none;
            }
            .post {
                width: 120px;
                height: 154px;
                position: absolute;
                left: 10px;
                top: 10px;
                object-fit: cover;
            }
            .author{
              position: absolute;
              top:125px;
              img{
              width: 30px;
              height: 30px;
              border-radius: 15px;
              }
            }
            .invite_author{
              font-size: var(--font-size-M);
              position: absolute;
              top:125px
            }
            .invite_icon{
              position: absolute;
              top:127px;
              right: 13.2vw;
              width: 15px;
              cursor: pointer;
            }
            .button_group{
                position: absolute;
                left: 160px;
                top: 130px;
                height: 30px;
                width: 600px;
            }
            .title {
                position: absolute;
                left: 160px;
                top: 20px;
                font-size: 16px;
                font-weight: 400;
            }

            .info {
                position: absolute;
                left: 160px;
                top: 40px;
                font-size: var(--font-size-M);
                color: #4F4F4F;

            }
        }
        .book_item:nth-child(1){
          margin-top: 0px !important;
        }
    }
}
</style>
