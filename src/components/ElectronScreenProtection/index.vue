<template>
  <div class="screen-protection"></div>
</template>

<script>
import Mousetrap from 'mousetrap'

export default {
  name: 'ScreenProtection',
  data() {
    return {
      isElectron: false,
      keydownHandler: null
    }
  },
  mounted() {
    this.isElectron = window.navigator.userAgent.includes('Electron')

    if (this.isElectron) {
      this.initElectronProtection()
    } else {
      this.initWebProtection()
    }
  },

  beforeDestroy() {
    this.cleanupProtection()
  },
  methods: {
    initElectronProtection() {

      if (window.ipc) {
        window.ipc.on('screenshot-attempt-detected', this.handleScreenshotAttempt)
      }
      this.setupKeybindingListeners()
      this.setupBasicWebProtection()
    },

    initWebProtection() {
      this.enableKeyboardProtection()
      this.setupKeybindingListeners()
    },

    setupBasicWebProtection() {
      this.enableKeyboardProtection()
    },

    enableKeyboardProtection() {
      this.keydownHandler = (e) => {
        const key = e.key?.toLowerCase()
        const keyCode = e.keyCode || e.which

        if (keyCode === 44 || key === 'printscreen') {
          e.preventDefault()
          this.handleScreenshotDetected(e)
          return false
        }
      }

      document.addEventListener('keydown', this.keydownHandler)
    },

    handleScreenshotDetected(event) {
      if (event) {
        event.preventDefault()
        event.stopPropagation()
      }
      this.showProtectionEffect()
    },

    showProtectionEffect() {
      if (this.$root.$showScreenProtection) {
        this.$root.$showScreenProtection({
          duration: 3000
        })
      }
    },

    cleanupProtection() {
      if (this.keydownHandler) {
        document.removeEventListener('keydown', this.keydownHandler)
      }

      Mousetrap.reset()

      if (this.isElectron && window.ipc) {
        window.ipc.removeAllListeners('screenshot-attempt-detected')
      }
    },
    handleScreenshotAttempt(data) {
      this.showProtectionEffect()
    },

    setupKeybindingListeners() {
      const commonKeys = [
        'printscreen',
        'ctrl+shift+s', 'control+shift+s', 'command+shift+s', 'meta+shift+s', 'cmd+shift+s'
      ]

      const electronKeys = [
        'command+shift+3', 'meta+shift+3', 'cmd+shift+3',
        'command+shift+4', 'meta+shift+4', 'cmd+shift+4',
        'command+shift+5', 'meta+shift+5', 'cmd+shift+5'
      ]

      Mousetrap.bind(commonKeys, (e) => {
        this.handleMousetrapScreenshot(e)
        return false
      })

      if (this.isElectron) {
        Mousetrap.bind(electronKeys, (e) => {
          this.handleMousetrapScreenshot(e)
          return false
        })
      }
    },

    handleMousetrapScreenshot(event) {
      if (event) {
        event.preventDefault()
        event.stopPropagation()
      }
      this.showProtectionEffect()
    }
  }
}
</script>

<style scoped lang="scss">
:global(.screenshot-blackscreen-mode) {
  background: #000 !important;
  * {
    visibility: hidden !important;
  }
}
</style>
