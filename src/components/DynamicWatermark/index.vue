<template>
  <div class="dynamic-watermark-component">
  </div>
</template>

<script>
export default {
  name: 'DynamicWatermark',

  props: {
    enabled: {
      type: Boolean,
      default: false
    },
    count: {
      type: Number,
      default: 50
    },
    positionUpdateInterval: {
      type: Number,
      default: 5000
    },
    contentUpdateInterval: {
      type: Number,
      default: 8000
    },
    customContents: {
      type: Array,
      default: () => []
    },
    userInfo: {
      type: String,
      default: ''
    },
    mobile: {
      type: String,
      default: ''
    },
    brandName: {
      type: String,
      default: '缤果课堂'
    }
  },

  data() {
    return {
      container: null,
      positionTimer: null,
      contentTimer: null,
      isActive: false
    }
  },

  computed: {
    watermarkContents() {
      const defaultContents = [
        `${this.userInfo || this.getUserInfo()}`,
        `${new Date().toLocaleDateString()}`,
        `${this.brandName}`
      ]

      if (this.mobile) {
        defaultContents.push(`${this.mobile}`)
      }

      return this.customContents.length > 0 ? this.customContents : defaultContents
    }
  },

  watch: {
    enabled: {
      handler(newVal) {
        if (newVal) {
          this.show()
        } else {
          this.hide()
        }
      },
      immediate: true
    }
  },

  beforeDestroy() {
    this.hide()
  },
  methods: {
    show() {
      if (this.isActive) {
        return
      }

      this.hide()
      this.createContainer()
      this.createWatermarks()

      this.startTimers()
      this.isActive = true
      this.$emit('watermark-shown')
    },

    hide() {
      if (this.container) {
        this.container.remove()
        this.container = null
      }

      this.clearTimers()

      this.isActive = false
      this.$emit('watermark-hidden')
    },

    createContainer() {
      this.container = document.createElement('div')
      this.container.id = 'dynamic-watermark-container'
      this.container.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 999998;
        overflow: hidden;
      `
      document.body.appendChild(this.container)
    },

    createWatermarks() {
      if (!this.container) return

      for (let i = 0; i < this.count; i++) {
        const watermark = document.createElement('div')
        watermark.className = 'enhanced-watermark'

        const x = Math.random() * 100
        const y = Math.random() * 100
        const rotation = Math.random() * 360
        const scale = 0.4 + Math.random() * 0.6
        const opacity = 0.1 + Math.random() * 0.15

        watermark.style.cssText = `
          position: absolute;
          left: ${x}%;
          top: ${y}%;
          transform: rotate(${rotation}deg) scale(${scale});
          color: rgba(150, 150, 150, ${opacity});
          font-size: ${14 + Math.random() * 6}px;
          font-weight: normal;
          font-family: Arial, sans-serif;
          text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
          transition: all 2s ease;
        `
        watermark.textContent = this.getRandomContent()
        this.container.appendChild(watermark)
      }
    },

    startTimers() {
      this.positionTimer = setInterval(() => {
        this.updatePositions()
      }, this.positionUpdateInterval)

      this.contentTimer = setInterval(() => {
        this.updateContents()
      }, this.contentUpdateInterval)
    },

    clearTimers() {
      if (this.positionTimer) {
        clearInterval(this.positionTimer)
        this.positionTimer = null
      }

      if (this.contentTimer) {
        clearInterval(this.contentTimer)
        this.contentTimer = null
      }
    },

    updatePositions() {
      const watermarks = document.querySelectorAll('.enhanced-watermark')
      if (watermarks.length === 0) {
        this.clearTimers()
        return
      }

      watermarks.forEach(watermark => {
        const newX = Math.random() * 100
        const newY = Math.random() * 100
        const newRotation = Math.random() * 360
        const newScale = 0.4 + Math.random() * 0.6
        const newOpacity = 0.1 + Math.random() * 0.15

        watermark.style.left = `${newX}%`
        watermark.style.top = `${newY}%`
        watermark.style.transform = `rotate(${newRotation}deg) scale(${newScale})`
        watermark.style.color = `rgba(150, 150, 150, ${newOpacity})`
      })
    },
    updateContents() {
      const watermarks = document.querySelectorAll('.enhanced-watermark')
      if (watermarks.length === 0) {
        this.clearTimers()
        return
      }

      watermarks.forEach(watermark => {
        watermark.textContent = this.getRandomContent()
      })
    },

    getRandomContent() {
      const contents = this.watermarkContents
      return contents[Math.floor(Math.random() * contents.length)]
    },

    getUserInfo() {
      return '用户' + Math.random().toString(36).substring(2, 6).toUpperCase()
    },

    generateSessionId() {
      return 'ID-' + Math.random().toString(36).substring(2, 8).toUpperCase()
    },

    refresh() {
      if (this.isActive) {
        this.updatePositions()
        this.updateContents()
      }
    },

    updateConfig(config) {
      const needRestart = this.isActive

      if (needRestart) {
        this.hide()
      }

      Object.keys(config).forEach(key => {
        if (this.$props.hasOwnProperty(key)) {
          this[key] = config[key]
        }
      })

      if (needRestart) {
        this.show()
      }
    }
  }
}
</script>

<style>

</style>
