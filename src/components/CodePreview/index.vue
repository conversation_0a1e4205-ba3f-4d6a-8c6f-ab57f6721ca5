<template>
  <div class="code-preview-component">
    <el-dialog
      title="代码预览"
      :visible="visible"
      width="90%"
      custom-class="code-preview-dialog"
      :close-on-click-modal="true"
      :destroy-on-close="true"
      :show-close="true"
      :append-to-body="true"
      top="1vh"
      @close="closeDialog"
      @closed="closeDialog"
    >
      <div class="preview-container">
        <iframe
          ref="previewFrame"
          sandbox="allow-scripts"
          frameborder="0"
          width="100%"
          height="100%"
        ></iframe>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'CodePreview',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    initialCode: {
      type: String,
      default: ''
    },
    language: {
      type: String,
      default: 'html'
    }
  },

  data() {
    return {
      executing: false
    }
  },

  watch: {
    visible(newVal) {
      if (newVal) {
        setTimeout(() => {
          this.executeCode()
        }, 100)
      }
    }
  },

  methods: {
    closeDialog() {
      this.$emit('update:visible', false)
      this.$nextTick(() => {
        const iframe = this.$refs.previewFrame
        if (iframe) {
          iframe.srcdoc = `
            <html>
            <body style="text-align: center; color: #999; padding: 50px;">
              <p>准备加载代码预览...</p>
            </body>
            </html>
          `
        }
      })
    },

    executeCode() {
      if (this.executing || !this.initialCode.trim()) {
        return
      }

      this.executing = true

      try {
        const previewHtml = this.generatePreviewHtml(this.initialCode)
        const iframe = this.$refs.previewFrame
        if (iframe) {
          iframe.srcdoc = ''
          setTimeout(() => {
            iframe.srcdoc = previewHtml
          }, 50)
        }
      } catch (error) {
        console.error('代码预览失败:', error)
      } finally {
        setTimeout(() => {
          this.executing = false
        }, 500)
      }
    },

    generatePreviewHtml(code) {
      let htmlContent = ''

      switch (this.language.toLowerCase()) {
        case 'html':
        case 'markup':
          htmlContent = code
          break
        case 'css':
          htmlContent = code
          break
        case 'javascript':
        case 'js':
          htmlContent = code
          break
        default:
          htmlContent = `<pre><code>${this.escapeHtml(code)}</code></pre>`
      }

      return `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>代码预览</title>
          <style>
            html, body {
              font-family: Arial, sans-serif;
              margin: 0;
              padding: 15px;
              background: #fff;
              line-height: 1.6;
              overflow-x: auto;
              overflow-y: auto;
              height: auto;
              min-height: 100%;
            }
          </style>
        </head>
        <body>
          ${htmlContent}
        </body>
        </html>
      `
    },

    escapeHtml(text) {
      const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
      }
      return text.replace(/[&<>"']/g, m => map[m])
    }
  }
}
</script>

<style lang="scss" scoped>
.preview-container {
  height: 100%;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

iframe {
  width: 100%;
  height: 100%;
}
</style>

<style lang="scss">
.code-preview-dialog {
  .el-dialog {
    height: 98vh !important;
  }

  .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    flex-shrink: 0;

    .el-dialog__title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .el-dialog__headerbtn {
      top: 15px;
      right: 15px;
      width: 32px;
      height: 32px;

      .el-dialog__close {
        font-size: 18px;
        color: #666;

        &:hover {
          color: #409eff;
        }
      }
    }
  }

  .el-dialog__body {
    padding: 0 !important;
    height: calc(98vh - 60px) !important;
  }
}
</style>
