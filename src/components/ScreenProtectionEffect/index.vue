<template>
  <div v-if="isVisible" class="screen-protection-effect">
  </div>
</template>

<script>
export default {
  name: 'ScreenProtectionEffect',

  data() {
    return {
      isVisible: false,
      hideTimer: null,
      isElectron: false
    }
  },

  mounted() {
    this.isElectron = window.navigator.userAgent.includes('Electron')
  },

  methods: {
    show(options = {}) {
      if (this.isVisible) {
        return
      }

      this.isVisible = true
      this.applyBodyEffect()

      const duration = options.duration || 3000

      this.hideTimer = setTimeout(() => {
        this.hide()
      }, duration)

      this.$emit('protection-shown', {
        type: 'blackscreen',
        environment: this.isElectron ? 'electron' : 'web',
        duration: duration
      })
    },

    hide() {
      this.isVisible = false
      this.removeBodyEffect()

      if (this.hideTimer) {
        clearTimeout(this.hideTimer)
        this.hideTimer = null
      }
    },

    applyBodyEffect() {
      document.body.classList.add('screenshot-blackscreen-mode')
    },

    removeBodyEffect() {
      document.body.classList.remove('screenshot-blackscreen-mode')
    }
  },

  beforeDestroy() {
    this.clearTimers()
    this.removeBodyEffect()
  }
}
</script>

<style scoped>
.screen-protection-effect {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000;
  z-index: 2147483647;
  pointer-events: none;
}
</style>

<style>
body.screenshot-blackscreen-mode {
  overflow: hidden !important;
}
</style>
