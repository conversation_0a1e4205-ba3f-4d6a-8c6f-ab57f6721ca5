<template>
  <div class="image-viewer-modal" :class="{ active: visible }" @click="handleModalClick">
    <div class="modal-content">
      <button class="close-btn" @click.stop="close">×</button>
      <button class="nav-btn prev-btn" @click.stop="prevImage" v-if="images.length > 1">←</button>

      <div class="slider-container">
        <div class="slider" :style="{ transform: `translateX(-${currentSlide * 100}%)` }">
          <div class="slide" v-for="(image, index) in images" :key="index">
            <img
              class="modal-img"
              :src="image"
              :alt="`图片 ${index + 1}`"
              :style="{
                transform: `translate(${translateX}px, ${translateY}px) scale(${currentScale})`,
                cursor: currentScale > 1 ? 'grab' : 'pointer'
              }"
              @touchstart="handleTouchStart"
              @touchmove="handleTouchMove"
              @touchend="handleTouchEnd"
              @mousedown="handleMouseDown"
              @mousemove="handleMouseMove"
              @mouseup="handleMouseUp"
              @mouseleave="handleMouseUp"
              @click="handleImageClick"
            >
          </div>
        </div>
      </div>

      <button class="nav-btn next-btn" @click.stop="nextImage" v-if="images.length > 1">→</button>

      <div class="zoom-controls">
        <button class="zoom-btn" @click.stop="zoomOut">-</button>
        <div class="zoom-value" @click.stop="resetZoom" :title="currentScale > 1 ? '点击重置' : ''">
          {{ Math.round(currentScale * 100) }}%
        </div>
        <button class="zoom-btn" @click.stop="zoomIn">+</button>
      </div>

      <div class="bottom-controls">
        <div class="counter">{{ currentSlide + 1 }}/{{ images.length }}</div>
        <div class="pagination" v-if="images.length > 1">
          <div
            class="dot"
            :class="{ active: index === currentSlide }"
            v-for="(image, index) in images"
            :key="index"
            @click.stop="goToSlide(index)"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ImageViewer',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    images: {
      type: Array,
      default: () => []
    },
    initialIndex: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      currentSlide: 0,
      currentScale: 1,
      initialDistance: 0,
      startX: 0,
      startY: 0,
      isDragging: false,
      isImageDragging: false,
      dragStartX: 0,
      dragStartY: 0,
      translateX: 0,
      translateY: 0,
      hasDragged: false,
      lastClickTime: 0
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.currentSlide = this.initialIndex
        this.resetZoom()
        this.hasDragged = false
        document.body.style.overflow = 'hidden'
      } else {
        document.body.style.overflow = ''
      }
    }
  },
  methods: {
    handleModalClick(e) {
      if (e.target === e.currentTarget || e.target.classList.contains('modal-img')) {
        this.close()
      }
    },

    close() {
      this.$emit('close')
    },

    prevImage() {
      this.currentSlide = (this.currentSlide - 1 + this.images.length) % this.images.length
      this.resetZoom()
    },

    nextImage() {
      this.currentSlide = (this.currentSlide + 1) % this.images.length
      this.resetZoom()
    },

    goToSlide(index) {
      this.currentSlide = index
      this.resetZoom()
    },

    zoomIn() {
      this.currentScale = Math.min(3, this.currentScale + 0.1)
      this.constrainImagePosition()
    },

    zoomOut() {
      this.currentScale = Math.max(0.5, this.currentScale - 0.1)
      this.constrainImagePosition()
    },

    resetZoom() {
      this.currentScale = 1
      this.translateX = 0
      this.translateY = 0
      this.hasDragged = false
    },

    handleTouchStart(e) {
      if (e.touches.length === 2) {
        e.preventDefault()
        this.initialDistance = this.getDistance(e.touches[0], e.touches[1])
      } else {
        this.startX = e.touches[0].clientX
        this.startY = e.touches[0].clientY
        this.isDragging = true

        if (this.currentScale > 1) {
          e.stopPropagation()
          this.isImageDragging = true
          this.dragStartX = e.touches[0].clientX
          this.dragStartY = e.touches[0].clientY
        }
      }
    },

    handleTouchMove(e) {
      if (e.touches.length === 2) {
        e.preventDefault()
        const currentDistance = this.getDistance(e.touches[0], e.touches[1])
        const scale = currentDistance / this.initialDistance

        this.currentScale *= scale
        this.currentScale = Math.max(0.5, Math.min(this.currentScale, 3))
        this.initialDistance = currentDistance
        this.constrainImagePosition()
      } else if (this.isImageDragging && this.currentScale > 1) {
        e.preventDefault()
        e.stopPropagation()

        const deltaX = e.touches[0].clientX - this.dragStartX
        const deltaY = e.touches[0].clientY - this.dragStartY

        this.translateX += deltaX
        this.translateY += deltaY

        this.dragStartX = e.touches[0].clientX
        this.dragStartY = e.touches[0].clientY
        this.hasDragged = true
      } else if (this.isDragging && this.images.length > 1 && this.currentScale <= 1) {
        const x = e.touches[0].clientX
        const diff = this.startX - x

        if (Math.abs(diff) > 50) {
          this.isDragging = false
          if (diff > 0) {
            this.nextImage()
          } else {
            this.prevImage()
          }
        }
      }
    },

    handleTouchEnd() {
      if (this.isImageDragging) {
        this.constrainImagePosition()
      }
      this.isDragging = false
      this.isImageDragging = false
    },

    getDistance(touch1, touch2) {
      const dx = touch1.clientX - touch2.clientX
      const dy = touch1.clientY - touch2.clientY
      return Math.sqrt(dx * dx + dy * dy)
    },

    handleMouseDown(e) {
      if (this.currentScale > 1) {
        e.preventDefault()
        e.stopPropagation()
        this.isImageDragging = true
        this.dragStartX = e.clientX
        this.dragStartY = e.clientY
        e.target.style.cursor = 'grabbing'
      }
    },

    handleMouseMove(e) {
      if (this.isImageDragging && this.currentScale > 1) {
        e.preventDefault()
        e.stopPropagation()

        const deltaX = e.clientX - this.dragStartX
        const deltaY = e.clientY - this.dragStartY

        this.translateX += deltaX
        this.translateY += deltaY

        this.dragStartX = e.clientX
        this.dragStartY = e.clientY
        this.hasDragged = true
      }
    },

    handleMouseUp(e) {
      if (this.isImageDragging) {
        e.stopPropagation()
        this.constrainImagePosition()
        this.isImageDragging = false
        e.target.style.cursor = this.currentScale > 1 ? 'grab' : 'pointer'
      }
    },

    handleImageClick(e) {
      const currentTime = Date.now()
      const timeDiff = currentTime - this.lastClickTime

      if (timeDiff < 300 && this.currentScale > 1) {
        e.stopPropagation()
        this.resetZoom()
      } else {
        if (this.hasDragged) {
          e.stopPropagation()
          this.hasDragged = false
        } else {
        }
      }

      this.lastClickTime = currentTime
    },

    constrainImagePosition() {
      if (this.currentScale <= 1) {
        this.translateX = 0
        this.translateY = 0
        return
      }

      const containerWidth = window.innerWidth * 0.9
      const containerHeight = window.innerHeight * 0.8

      const baseImageWidth = containerWidth * 0.8
      const baseImageHeight = containerHeight * 0.8

      const extraWidth = baseImageWidth * (this.currentScale - 1)
      const extraHeight = baseImageHeight * (this.currentScale - 1)

      const maxMoveX = extraWidth / 2
      const maxMoveY = extraHeight / 2

      this.translateX = Math.max(-maxMoveX, Math.min(maxMoveX, this.translateX))
      this.translateY = Math.max(-maxMoveY, Math.min(maxMoveY, this.translateY))
    }
  },
  beforeDestroy() {
    document.body.style.overflow = ''
  }
}
</script>

<style lang="scss" scoped>
.image-viewer-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s;

  &.active {
    opacity: 1;
    pointer-events: all;
  }
}

.modal-content {
  position: relative;
  width: 90%;
  height: 90%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.slider-container {
  position: relative;
  width: 100%;
  height: 80vh;
  overflow: hidden;
}

.slider {
  display: flex;
  height: 100%;
  transition: transform 0.5s ease;
}

.slide {
  min-width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-img {
  max-width: 100%;
  max-height: 100%;
  transform-origin: center center;
  transition: transform 0.3s;
  cursor: pointer;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-top: 15px;
  background: rgba(255, 255, 255, 0.8);
  padding: 10px 15px;
  border-radius: 30px;
}

.zoom-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #2c3e50;
  color: white;
  border: none;
  font-size: 20px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background 0.3s;

  &:hover {
    background: #1a252f;
  }
}

.zoom-value {
  min-width: 50px;
  text-align: center;
  font-weight: bold;
  cursor: pointer;
  padding: 5px;
  border-radius: 5px;
  transition: background 0.3s;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
}

.pagination {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: background 0.3s;

  &.active {
    background: white;
  }
}

.nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 24px;
  cursor: pointer;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;

  &.prev-btn {
    left: 20px;
  }

  &.next-btn {
    right: 20px;
  }
}

.close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 80px;
  height: 80px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 32px;
  cursor: pointer;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background 0.3s;

  &:hover {
    background: rgba(0, 0, 0, 0.8);
  }
}

.bottom-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  margin-top: 15px;
}

.counter {
  background: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 14px;
}
</style>
