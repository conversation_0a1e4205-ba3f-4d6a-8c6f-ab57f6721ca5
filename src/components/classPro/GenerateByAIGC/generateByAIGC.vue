<template>
  <NormalDialog
    v-if="dialogShow"
    :title="title"
    width="580px"
    :dialog-visible="dialogShow"
    :is-center="true"
    :append-to-body="true"
    :dig-class="true"
    @closeDialog="close"
  >
    <div class="no-conversion">
      <div class='main-body'>
        <div class='left'>
          <i class="el-icon-warning-outline" style='font-size: 30px;color:#FF7F28;'></i>
        </div>
        <div class='right'>
          <div class='right_title'>
            {{ hasData ? contentTitle.title0 : contentTitle.title1 }}
          </div>
          <div class='right_content'>
            <div>{{ contentObj.content0 }}</div>
            <div>{{ contentObj.content1 }}</div>
          </div>
          <div class='right_tip' v-if='hasData'>
            {{ contentTip }}
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="editor-dig">
        <el-button v-if='!hasData' type="primary" size="small" style='min-width: 100px' @click='handleNotify(1)'>立即生成</el-button>
        <el-button v-else type="primary" size="small" style='min-width: 100px' @click='handleNotify(1)'>立即生成</el-button>
        <el-button size="small" style='min-width: 100px;margin-left: 50px' @click='close'>取消</el-button>
      </div>
    </template>
  </NormalDialog>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2'
export default {
  components: { NormalDialog },
  props: {
    dataId: {
      type: [String, Number],
      default: 0
    },
    token: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: '生成数据'
    },
    contentTitle: {
      type: Object,
      default: () => {
        return {
          title0: '已有内容，确定重新生成并覆盖：',
          title1: 'AI生成提示：'
        }
      }
    },
    contentObj: {
      type: Object,
      default: () => {
        return {
          content0: '①确定为最终版本',
          content1: '②生成过程中时间较长，耐心等待'
        }
      }
    },
    contentTip: {
      type: String,
      default: '*提交后会删除原内容，不可恢复'
    },
  },
  data() {
    return {
      dialogShow: false,
      loading: false,
      hasData: false
    }
  },
  methods: {
    close() {
      this.dialogShow = false
    },
    show(hasData = false) {
      this.dialogShow = true
      this.hasData = hasData
    },
    handleNotify(type) {
      // this.$refs.generateNotifRef.show(type)
      this.$emit('handleNotify', type)
    }
  }
}
</script>

<style scoped lang='scss'>
.no-conversion{
  width: 100%;
  .main-body{
    display: flex;
    padding: 20px 50px 50px 50px;
    .left{
      height: 100%;
      width: 30px;
    }
    .right{
      height: 100%;
      width: calc(100% - 30px);
      font-size: 20px;
      .right_title{
        width: 100%;
        height: 30px;
        line-height: 30px;
        font-weight: 600;
        color: #000;
        padding-left: 10px;
      }
      .right_content{
        width: 100%;
        padding-left: 10px;
        color: #000;
        margin-top: 20px;
      }
      .right_tip{
        width: 100%;
        color: #EB5757;
        margin-top: 20px;
      }
    }
  }
}

.editor-dig{
  width: 100%;
  display: flex;
  justify-content: center;
  ::v-deep .el-button--small{
    font-size: 18px;
  }
}

</style>
