<template>
  <el-input
    v-model="innerValue"
    type="textarea"
    :rows="minRows"
    :autosize="false"
    @input="handleInput"
    :style="{ height: textareaHeight + 'px', width: '100% '}"
    :placeholder="placeholder"
    :maxlength="maxlength"
    :show-word-limit="showWordLimit"
    ref="textarea"
  />
</template>

<script>
export default {
  name: 'AutoResizeTextarea',
  props: {
    // 接收v-model传递的值
    value: {
      type: String,
      default: ''
    },
    // 最小行数
    minRows: {
      type: Number,
      default: 2
    },
    // 最大行数，超过则显示滚动条
    maxRows: {
      type: Number,
      default: 6
    },
    placeholder: {
      type: String,
      default: '请输入内容'
    },
    maxlength: {
      type: Number,
      default: 500 // 最大字符数
    },
    showWordLimit: {
      type: Boolean,
      default: true // 是否显示字数限制
    }
  },
  data() {
    return {
      innerValue: this.value, // 内部维护的值
      textareaHeight: 0,
      // 保存上一次的高度用于对比
      lastHeight: 0
    }
  },
  mounted() {
    // 初始化高度
    this.$nextTick(() => {
      this.adjustTextareaHeight()
    })
  },
  methods: {
    adjustTextareaHeight() {
      const textarea = this.$refs.textarea?.$refs.textarea
      if (!textarea) return
      const scrollTop = textarea.scrollTop
      // 先将高度设为auto，让其自适应内容
      textarea.style.height = '0px'
      textarea.style.height = '100%'
      // 计算单行高度
      const singleLineHeight = textarea.scrollHeight / this.minRows
      // 计算内容需要的高度
      let newHeight = textarea.scrollHeight
      // 限制最小高度
      const minHeight = singleLineHeight * this.minRows
      newHeight = Math.max(newHeight, minHeight)
      console.log(newHeight, textarea.scrollHeight)
      // 限制最大高度
      if (this.maxRows) {
        const maxHeight = singleLineHeight * this.maxRows
        newHeight = Math.min(newHeight, maxHeight)
      }
      // 解决内容减少时高度调整不及时的问题
      if (newHeight < this.lastHeight) {
        newHeight = newHeight - 21
      }
      this.textareaHeight = newHeight
      this.lastHeight = newHeight
      textarea.scrollTop = scrollTop
    },
    // 处理输入事件，向父组件emit值的变化
    handleInput() {
      this.adjustTextareaHeight()
      // 通过input事件将内部值传递给父组件
      this.$emit('input', this.innerValue)
    }
  },
  watch: {
    // 当外部value变化时同步到内部
    value(newVal) {
      this.innerValue = newVal
      this.$nextTick(() => {
        this.adjustTextareaHeight()
      })
    }
  }
}
</script>

<style scoped lang='scss'>
::v-deep .el-textarea__inner {
  resize: none !important; /* 禁用手动调整大小 */
  overflow-y: auto;
  transition: height 0.2s ease-in-out; /* 平滑过渡效果 */
}
</style>
