<template>
  <div class='scratch_main' v-if='showScratch'>
    <div class='header_view'>
      {{ experimentTitle }}
      <div class='back' @click='back'>
        <i class="el-icon-arrow-left"></i>
        返回
      </div>
      <div class='option'>
        <div class='option_btn' @click='openDialog'>
          <svg-icon class='svgIcon' icon-class="explanation" class-name="explanation" />
          实验说明
        </div>
        <div class='option_btn' @click="handleReset">
          <svg-icon class='svgIcon' icon-class='reset' class-name="reset" />
          重置
        </div>
      </div>
    </div>
    <div class='content_view'>
      <iframe
        id="scratch-iframe"
        ref="scratchFrame"
        :src="scratchUrl"
        style="border: none"
        width="100%"
        height="100%"
        allowfullscreen
        allow="microphone *; camera *"
        sandbox="allow-same-origin allow-scripts allow-popups allow-modals allow-forms allow-downloads allow-presentation"
      ></iframe>
    </div>
    <NormalDialog
      v-if="dialogShow"
      :title="experimentTitle"
      width="920px"
      :dialog-visible="dialogShow"
      :is-center="true"
      :append-to-body="true"
      :dig-class="true"
      @closeDialog="closeDialog"
    >
      <div class="no-conversion" v-loading="loading">
        <div class="explanation_left ">
          <video v-if="videoUrl && videoUrl !== ''" :src="videoUrl" style="width: 100%;height: 50%;border-radius: 20px" controls :poster="videoPoster"></video>
          <el-image v-else style="width: 100%;height: 50%;border-radius: 20px" :src="DefaultCover" fit="cover"/>
          <div class='know_btn' @click='dialogShow = false'>知道了</div>
        </div>
        <div class="explanation_right">
          <div class="title">实验说明:</div>
          <div class="des" v-html="trainingData ? trainingData.description : ''">
          </div>
        </div>
      </div>
    </NormalDialog>
  </div>
</template>

<script>
import NormalDialog from '@/components/classPro/NormalDialog/index2'
import { getToken } from 'utils/auth'
import { getTraining } from '@/api/training-api'
import DefaultCover from '@/assets/scratch/explanationDefault.png'
export default {
  components: {
    NormalDialog
  },
  props: {
    studentCourseId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      DefaultCover,
      showScratch: false,
      scratchFile: '',
      dialogShow: true,
      experimentTitle: '',
      videoUrl: '',
      videoPoster: '',
      token: '',
      loading: false,
      trainingData: null
    }
  },
  mounted() {
    this.token = this.$route.query && this.$route.query.token ? `Bearer ${this.$route.query.token}` : getToken()
  },
  methods: {
    async open(id) {
      this.showScratch = true
      this.dialogShow = true
      await this.getTrainingInfo(id)
    },
    back() {
      this.showScratch = false
      this.trainingData = null
      this.$emit('close')
    },
    closeDialog() {
      this.dialogShow = false
    },
    openDialog() {
      this.dialogShow = true
    },
    handleReset() {
      this.$confirm('确定要重置项目吗？所有更改将丢失。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const iframe = document.getElementById('scratch-iframe')
        iframe.contentWindow.postMessage({
          type: 'RESET_PROJECT',
          timestamp: Date.now()
        }, 'https://scratch-qa.binguoketang.com')
      })
    },
    async getTrainingInfo(id) {
      this.loading = true
      try {
        const { data } = await getTraining({
          trainingId: id,
          studentCourseId: this.studentCourseId
        }, { authorization: this.token })
        if (!data) {
          this.$message.warning('该实验已被删除')
          this.back()
          return
        }
        this.trainingData = data
        this.videoUrl = data.descriptionVideo
        this.scratchFile = data.scratchFile
        this.experimentTitle = data.trainingName
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    }
  },
  computed: {
    scratchUrl() {
      return `${process.env.VUE_APP_SCRATCH_URL}?project=${this.scratchFile}`
    }
  }
}
</script>

<style scoped lang='scss'>
.scratch_main{
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 99;
  background: white;
  .header_view{
    width: 100%;
    height: 40px;
    background: #0a1f3d;
    color: white;
    font-size: var(--font-size-XXL);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    .back{
      position: absolute;
      left: 15px;
      font-size: var(--font-size-XL);
      color: #fff;
      cursor: pointer;
    }
    .option{
      position: absolute;
      right: 15px;
      font-size: var(--font-size-XL);
      color: #fff;
      display: flex;
      .option_btn{
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        color: #fff;
        font-size: var(--font-size-M);
        margin-left: 10px;
      }
      .svgIcon{
        width: 20px;
        height: 20px;
      }
    }
  }
  .content_view{
    width: 100%;
    height: calc(100% - 40px);
    padding: 0 15px 15px 15px;
    background: #0a1f3d;
  }
}
.no-conversion{
  width: 100%;
  height: 50vh;
  display: flex;
  .explanation_left{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .know_btn{
      height: 40px;
      width: 50%;
      color: white;
      background: linear-gradient(90deg, #36D1DC 0%, #5B86E5 100%);
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 5px;
      margin-top: 10vh;
    }
  }
  .explanation_right{
    width: 100%;
    height: 100%;
    font-size: var(--font-size-L);
    padding-left: 20px;
    .title{
      font-weight: 600;
      margin-bottom: 5px;
      height: 30px;
    }
    .des{
      padding-left: 5px;
      overflow-y: auto;
      width: 100%;
      height: calc(100% - 35px);
    }
  }
}
</style>
