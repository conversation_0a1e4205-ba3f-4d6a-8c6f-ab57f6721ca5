
import request from '@/utils/request'

export function getSqlPlatformToken (params, headers = {}) {
  return request({
    url: '/api/v2/thirdparty/vt/getSqlPlatformToken',
    method: 'get',
    params,
    headers
  })
}

export function training (data, headers = {}) {
  return request({
    url: '/api/v1/training/training',
    method: 'post',
    data,
    headers
  })
}
export function deleteTraining (params, data, headers = {}) {
  return request({
    url: '/api/v1/training/training',
    method: 'post',
    params,
    data,
    headers
  })
}

export function trainingStep (params, data, headers = {}) {
  return request({
    url: '/api/v1/training/trainingStep',
    method: 'post',
    params,
    data,
    headers
  })
}

export function updateTrainingStepProgress (params, headers = {}) {
  return request({
    url: '/api/v1/training/updateTrainingStepProgress',
    method: 'post',
    params,
    headers
  })
}

export function getTraining (params, headers = {}) {
  return request({
    url: '/api/v1/training/vt/getTraining',
    method: 'get',
    params,
    headers
  })
}

export function applyTrainingOffice (params, headers = {}) {
  return request({
    url: '/api/v1/training/applyTrainingOffice',
    method: 'post',
    params,
    headers
  })
}
export function redoTrainingStep (params, headers = {}) {
  return request({
    url: '/api/v1/training/redoTrainingStep',
    method: 'post',
    params,
    headers
  })
}
export function getUserBookTrainingData (params, headers = {}) {
  return request({
    url: '/api/v1/training/getUserBookTrainingData',
    method: 'get',
    params,
    headers
  })
}

export function updateTrainingResult (params, headers = {}) {
  return request({
    url: '/api/v1/training/updateTrainingResult',
    method: 'post',
    params,
    headers
  })
}
export function updateTrainingUserScore (params, headers = {}) {
  return request({
    url: '/api/v1/training/updateUserTrainingScore',
    method: 'post',
    params,
    headers
  })
}
export function dragTrainingStep (params, headers = {}) {
  return request({
    url: '/api/v1/training/dragTrainingStep',
    method: 'post',
    params,
    headers
  })
}
export function getTrainingAiChat (params, headers = {}) {
  return request({
    url: '/api/v1/training/getTrainingAiChat',
    method: 'get',
    params,
    headers
  })
}
export function updateTrainingAigcImg (params, headers = {}) {
  return request({
    url: '/api/v1/training/updateTrainingAigcImg',
    method: 'post',
    params,
    headers
  })
}

export function getTrainingCaseList (params, headers = {}) {
  return request({
    url: '/api/v1/training/vt/getTrainingCaseList',
    method: 'get',
    params,
    headers
  })
}

export function getTrainingCaseTemplateList (headers = {}) {
  return request({
    url: '/api/v1/training/vt/getTrainingCaseTemplateList',
    method: 'get',
    headers
  })
}
