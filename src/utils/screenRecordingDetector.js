// 简单的录屏检测器 - 测试版
class ScreenRecordingDetector {
  constructor() {
    this.isDetecting = false;
    this.detectedSoftware = [];
  }

  // 启动检测
  startDetection() {
    if (this.isDetecting) return;
    
    this.isDetecting = true;
    console.log('🔍 开始录屏检测...');
    
    // 检测浏览器API调用
    this.interceptScreenCaptureAPI();
    
    // 监控页面状态变化
    this.monitorPageVisibility();
    
    // 检测开发者工具
    this.detectDevTools();
    
    // 如果是Electron环境，启动进程检测
    if (window.require) {
      this.startElectronDetection();
    }
  }

  // 拦截屏幕捕获API
  interceptScreenCaptureAPI() {
    if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
      const originalGetDisplayMedia = navigator.mediaDevices.getDisplayMedia;
      
      navigator.mediaDevices.getDisplayMedia = (...args) => {
        console.log('🚨 检测到 getDisplayMedia 调用 - 可能在录屏');
        this.onRecordingDetected('Browser Screen Capture API');
        return originalGetDisplayMedia.apply(navigator.mediaDevices, args);
      };
    }

    // 拦截getUserMedia（某些录屏软件会用这个）
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      const originalGetUserMedia = navigator.mediaDevices.getUserMedia;
      
      navigator.mediaDevices.getUserMedia = (constraints) => {
        if (constraints && constraints.video && constraints.video.mediaSource === 'screen') {
          console.log('🚨 检测到 getUserMedia 屏幕捕获');
          this.onRecordingDetected('Browser getUserMedia Screen');
        }
        return originalGetUserMedia.apply(navigator.mediaDevices, [constraints]);
      };
    }
  }

  // 监控页面可见性变化
  monitorPageVisibility() {
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        console.log('📱 页面失去焦点，可能在录屏');
        // 延迟检测，避免正常切换标签页的误判
        setTimeout(() => {
          if (document.hidden) {
            this.onRecordingDetected('Page Hidden - Possible Recording');
          }
        }, 3000);
      } else {
        console.log('📱 页面重新获得焦点');
        this.onRecordingRestored('Page Visible Again');
      }
    });

    // 监控窗口焦点
    window.addEventListener('blur', () => {
      console.log('🔍 窗口失去焦点');
    });

    window.addEventListener('focus', () => {
      console.log('🔍 窗口重新获得焦点');
    });
  }

  // 检测开发者工具
  detectDevTools() {
    let devtools = { open: false };
    
    setInterval(() => {
      const widthThreshold = 160;
      const heightThreshold = 160;
      
      if (window.outerHeight - window.innerHeight > heightThreshold || 
          window.outerWidth - window.innerWidth > widthThreshold) {
        if (!devtools.open) {
          devtools.open = true;
          console.log('🔧 检测到开发者工具打开');
          this.onRecordingDetected('Developer Tools Opened');
        }
      } else {
        if (devtools.open) {
          devtools.open = false;
          console.log('🔧 开发者工具已关闭');
          this.onRecordingRestored('Developer Tools Closed');
        }
      }
    }, 1000);
  }

  // Electron环境的进程检测
  startElectronDetection() {
    try {
      const { exec } = window.require('child_process');
      const os = window.require('os');
      
      console.log('🖥️ Electron环境检测到，启动系统级检测');
      
      setInterval(() => {
        this.checkSystemProcesses(exec, os);
      }, 3000);
      
    } catch (error) {
      console.log('❌ Electron API访问失败:', error.message);
      console.log('💡 可能需要在主进程中实现检测');
    }
  }

  // 检查系统进程
  checkSystemProcesses(exec, os) {
    const platform = os.platform();
    let command;
    
    if (platform === 'win32') {
      // Windows: 检测常见录屏软件
      command = 'tasklist /FO CSV | findstr /I "obs,bandicam,camtasia,fraps,xsplit"';
    } else if (platform === 'darwin') {
      // macOS: 检测录屏软件
      command = 'ps aux | grep -E "(QuickTime|OBS|Camtasia|ScreenFlow)" | grep -v grep';
    } else {
      // Linux: 检测录屏软件
      command = 'ps aux | grep -E "(obs|recordmydesktop|kazam)" | grep -v grep';
    }
    
    exec(command, (error, stdout) => {
      if (!error && stdout.trim()) {
        console.log('🚨 系统级检测到录屏软件:');
        console.log(stdout);
        this.onRecordingDetected('System Process Detection');
      }
    });
  }

  // 录屏检测到时的回调
  onRecordingDetected(method) {
    console.log(`🚨 录屏行为检测到! 方法: ${method}`);
    console.log(`⏰ 时间: ${new Date().toLocaleString()}`);
    
    // 这里可以添加视频保护逻辑
    // 比如：视频变黑、显示警告等
    
    // 触发自定义事件
    window.dispatchEvent(new CustomEvent('screenRecordingDetected', {
      detail: { method, timestamp: Date.now() }
    }));
  }

  // 录屏停止时的回调
  onRecordingRestored(method) {
    console.log(`✅ 录屏行为停止! 方法: ${method}`);
    console.log(`⏰ 时间: ${new Date().toLocaleString()}`);
    
    // 触发自定义事件
    window.dispatchEvent(new CustomEvent('screenRecordingRestored', {
      detail: { method, timestamp: Date.now() }
    }));
  }

  // 停止检测
  stopDetection() {
    this.isDetecting = false;
    console.log('🔍 录屏检测已停止');
  }
}

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ScreenRecordingDetector;
} else {
  window.ScreenRecordingDetector = ScreenRecordingDetector;
}
