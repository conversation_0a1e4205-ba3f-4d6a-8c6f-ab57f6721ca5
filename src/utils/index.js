/**
 * @description: 计算上课教室显示的长宽以及border基本宽度
 * @param {number} fixedRatio
 * @param {HTMLElement} dom
 * @return {*}
 */
export function handleClassRoomResize (fixedRatio, dom) {
  let width = document.documentElement.clientWidth
  let height = document.documentElement.clientHeight
  const ratio = width / height
  if (ratio < 1) {
    width = document.documentElement.clientHeight
    height = document.documentElement.clientWidth
  }
  if (!dom) return
  let borderWidth = 12
  if (ratio > fixedRatio) {
    dom.style.width = `${height * fixedRatio}px`
    dom.style.height = `${height}px`
    borderWidth = height * fixedRatio * 0.0125
  } else {
    dom.style.width = `${width}px`
    dom.style.height = `${width / fixedRatio}px`
    borderWidth = width * 0.0125
  }
  return borderWidth
}

export function isFileSizeGreaterThan200MB(sizeStr) {
  const sizeUnit = sizeStr.slice(-2).toUpperCase() // 获取单位
  const sizeValue = parseFloat(sizeStr) // 获取数值

  // 将大小转换为 MB
  let sizeInMB

  switch (sizeUnit) {
    case 'GB':
      sizeInMB = sizeValue * 1024
      break
    case 'MB':
      sizeInMB = sizeValue
      break
    case 'KB':
      sizeInMB = sizeValue / 1024
      break
    case 'B':
      sizeInMB = sizeValue / (1024 * 1024)
      break
    default:
      throw new Error('Unsupported size unit')
  }

  return sizeInMB > 200 // 判断是否大于 200MB
}

export function getFileType(fileName) {
  // 获取文件后缀名
  const extension = fileName.split('.').pop().toLowerCase()
  // 定义不同类型的文件后缀
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp']
  const videoExtensions = ['mp4', 'avi', 'mov', 'mkv', 'flv', 'wmv']
  const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a']
  const officeExtensions = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf']
  const archiveExtensions = ['zip', 'rar', '7z', 'tar', 'gz']

  // 判断文件类型
  if (imageExtensions.includes(extension)) {
    return 'img'
  } else if (videoExtensions.includes(extension)) {
    return 'video'
  } else if (audioExtensions.includes(extension)) {
    return '音频'
  } else if (officeExtensions.includes(extension)) {
    return 'Office'
  } else if (archiveExtensions.includes(extension)) {
    return '压缩文件'
  } else {
    return '其他类型'
  }
}

export function jsonParse (json) {
  try {
    return JSON.parse(json)
  } catch (err) {
    return {}
  }
}

let id = 0
export function roundUid () {
  if (!id) {
    id = Math.ceil((Date.now() / 1000)) + Math.ceil((Math.random() * 1000) % 1000)
  }
  return id
}

export function imgScaling (imgWidth, imgHeight, containerWidth, containerHeight) {
  let [
    // 用于设定图片的宽和高
    tempWidth,
    tempHeight
  ] = [
    undefined,
    undefined
  ]
  try {
    imgWidth = parseFloat(imgWidth)
    imgHeight = parseFloat(imgHeight)
    containerWidth = parseFloat(containerWidth)
    containerHeight = parseFloat(containerHeight)
  } catch (error) {
    throw new Error('抱歉，我只接收数值类型或者可以转成数值类型的参数')
  }

  if (imgWidth > 0 && imgHeight > 0) {
    // 原图片宽高比例 大于 指定的宽高比例，这就说明了原图片的宽度必然 > 高度
    if ((imgWidth / imgHeight) >= (containerWidth / containerHeight)) {
      // if (imgWidth > containerWidth) {
      //   console.log('imgScaling', 1)
      //   tempWidth = containerWidth
      //   // 按原图片的比例进行缩放
      //   tempHeight = (imgHeight * containerWidth) / imgWidth
      // } else {
      //   console.log('imgScaling', 2)
      //   // 按照图片的大小进行缩放
      //   tempWidth = imgWidth
      //   tempHeight = imgHeight
      // }
      tempWidth = containerWidth
      // 按原图片的比例进行缩放
      tempHeight = (imgHeight * containerWidth) / imgWidth
    } else { // 原图片的高度必然 > 宽度
      // if (imgHeight > containerHeight) {
      //   console.log('imgScaling', 3)
      //   tempHeight = containerHeight
      //   // 按原图片的比例进行缩放
      //   tempWidth = (imgWidth * containerHeight) / imgHeight
      // } else {
      //   console.log('imgScaling', 4)
      //   // 按原图片的大小进行缩放
      //   tempWidth = imgWidth
      //   tempHeight = imgHeight
      // }
      tempHeight = containerHeight
      // 按原图片的比例进行缩放
      tempWidth = (imgWidth * containerHeight) / imgHeight
    }
  }

  return { tempWidth, tempHeight }
}

// 防抖 immediate 是否开始立即执行
export function debounce (func, wait = 3000, immediate = false) {
  let timeout

  return function () {
    const context = this
    const args = arguments

    if (timeout) clearTimeout(timeout) // timeout 不为null
    if (immediate) {
      const callNow = !timeout // 第一次会立即执行，以后只有事件执行后才会再次触发
      timeout = setTimeout(function () {
        timeout = null
      }, wait)
      if (callNow) {
        func.apply(context, args)
      }
    } else {
      timeout = setTimeout(function () {
        func.apply(context, args)
      }, wait)
    }
  }
}

export const throttle = (() => {
  let timer = null
  return (callback, wait) => {
    if (!timer) {
      // 将外部传入的函数的执行放在setTimeout中
      callback.apply(this)
      timer = setTimeout(() => {
        // 调用传入的函数fn
        // 最后在setTimeout执行完毕后再把标记timer设置为null,表示可以执行下一次循环了。
        timer = null
      }, wait)
    }
  }
})()

export function initWechatShare (res, shareOptions) {
  var wx = require('jweixin-module')
  wx.config({
    beta: true,
    debug: false,
    appId: res.data.appId,
    timestamp: res.data.timestamp,
    nonceStr: res.data.nonceStr,
    signature: res.data.signature,
    jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData']
  })

  wx.ready(function () {
    wx.updateAppMessageShareData(shareOptions)
    wx.updateTimelineShareData(shareOptions)
  })
}

export function downloadFile (path, name) {
  throttle(function () {
    const xhr = new XMLHttpRequest()
    xhr.open('get', path)
    xhr.responseType = 'blob'
    xhr.send()
    xhr.onload = function () {
      if (this.status === 200 || this.status === 304) {
        const fileReader = new FileReader()
        fileReader.readAsDataURL(this.response)
        fileReader.onload = function () {
          const a = document.createElement('a')
          a.style.display = 'none'
          a.href = this.result
          a.download = name
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
        }
      }
    }
  }, 2000)
}
export function isWeChatBrowser() {
  const ua = navigator.userAgent.toLowerCase()
  return /micromessenger/i.test(ua) && !/wxwork/i.test(ua)
}
export function isApp() {
  return (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.bingo_action) || window.bingo_action
}

// 最简单的录屏检测 - 只打日志测试
export function initScreenRecordingDetection() {
  console.log('🔍 录屏检测初始化');

  // 检测1: 拦截getDisplayMedia API
  if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
    const originalGetDisplayMedia = navigator.mediaDevices.getDisplayMedia;

    navigator.mediaDevices.getDisplayMedia = function(...args) {
      console.log('🚨 检测到getDisplayMedia调用');
      console.log('时间:', new Date().toLocaleTimeString());
      return originalGetDisplayMedia.apply(this, args);
    };

    console.log('✅ API拦截设置完成');
  } else {
    console.log('❌ 浏览器不支持getDisplayMedia');
  }
}
