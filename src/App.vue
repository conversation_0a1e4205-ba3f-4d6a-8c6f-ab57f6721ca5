<template>
  <div id="app" style="background:rgba(202,224,249, 0.4);" @contextmenu="handleContextmenu($event)">
    <div v-if="showMobileTips" class="moblie_tips">
      <img class="logo" src="./assets/bingoBook/app_logo.png" alt="" />
      <p>缤果数字教材</p>
      <div class="open" @click="toApp">APP内打开</div>
      <img class="close" src="./assets/H5/cancle.png" alt="" @click="showMobileTips=false" />
    </div>
    <div v-if="showMobileTipsH5" class="moblie_tips">
      <img class="logo" src="./assets/bingoMate/mate_icon.png" alt="" />
      <p>缤果学伴</p>
      <div class="open" @click="toAppBingoMate">APP内打开</div>
      <img class="close" src="./assets/H5/cancle.png" alt="" @click="showMobileTipsH5=false" />
    </div>
    <keep-alive>
      <router-view :key="$route.fullPath" v-if="$route.meta.keepAlive" />
    </keep-alive>
    <router-view :key="$route.fullPath" v-if="!$route.meta.keepAlive" />
    <!-- <update-dialog /> -->

    <ElectronScreenProtection />

    <DynamicWatermark
      ref="dynamicWatermark"
      :enabled="false"
      :brand-name="'缤果课堂'"
    />
    <ScreenProtectionEffect ref="screenProtectionEffect" />

  </div>
</template>

<script>
// import UpdateDialog from '@/components/classPro/UpdateDialog/index.vue'
import { isApp, isWeChatBrowser } from '@/utils/index.js'
import titleManager from '@/utils/titleManager'
import CallApp from 'callapp-lib'
import ElectronScreenProtection from '@/components/ElectronScreenProtection/index.vue'
import DynamicWatermark from '@/components/DynamicWatermark/index.vue'
import ScreenProtectionEffect from '@/components/ScreenProtectionEffect/index.vue'

export default {
  components: {
    ElectronScreenProtection,
    DynamicWatermark,
    ScreenProtectionEffect
  },
  data() {
    return {
      showMobileTips: false,
      showMobileTipsH5: false,
      showBottomDeepLink: false,
      callAppInstance: null
    }
  },
  mounted() {
    document.querySelector('.loading').style.opacity = '0'
    this.setTitle()
    this.initCallApp()
    this.setupGlobalProtection()
  },
  /**
   * 处理兼容app嵌入
   *
   * @returns {void} 无返回值
   */
  updated() {
    if (this.$route.meta.title === 'BingoBook' && !isApp() && !sessionStorage.getItem('h5FistTips')) {
      this.showMobileTips = true
      sessionStorage.setItem('h5FistTips', true)
    }
    if (this.$route.meta.type === 'H5' && !isApp() && !sessionStorage.getItem('h5FistTips')) {
      this.showMobileTipsH5 = true
      sessionStorage.setItem('h5FistTips', true)
    }
    if (this.$route.meta.title === 'BingoBook' && !isApp()) {
      this.showBottomDeepLink = true
    } else {
      this.showBottomDeepLink = false
    }
  },

  methods: {
    async setTitle() {
      if (window.ipc) {
        const res = await window.ipc.invoke('getChannelConfig')
        // 确保渠道配置被正确设置到 localStorage
        if (res) {
          window.localStorage.setItem('channel', res)
        }

        await titleManager.init()

        if (res === 'digitalbook') {
          // document.title = '缤果数字教材'
          document.title = ''

          this.setFavicon('/icon.ico')
        } else {
          this.setFavicon('/icon.ico')
        }
      } else {
        const channel = window.localStorage.getItem('channel')

        await titleManager.init(
          { showInTitle: false, showInAbout: true },
          '缤果课堂'
        )

        if (channel === 'digitalbook') {
          // document.title = '缤果数字教材'
          document.title = ''

          this.setFavicon('/icon.ico')
        } else {
          this.setFavicon('/icon.ico')
        }
      }
      this.$titleManager = titleManager
    },
    setFavicon(iconPath) {
      const existingIcons = document.querySelectorAll("link[rel*='icon']")
      existingIcons.forEach(icon => icon.remove())
      const link = document.createElement('link')
      link.rel = 'icon'
      link.type = 'image/x-icon'
      link.href = iconPath
      document.head.appendChild(link)
    },
    handleContextmenu (e) {
      e.preventDefault()
      return false
    },

    setupGlobalProtection() {
      this.$root.$showScreenProtection = (options) => {
        if (this.$refs.screenProtectionEffect) {
          this.$refs.screenProtectionEffect.show(options)
        }
      }

      this.$root.$hideScreenProtection = () => {
        if (this.$refs.screenProtectionEffect) {
          this.$refs.screenProtectionEffect.hide()
        }
      }

      this.$root.$showWatermark = (options = {}) => {
        if (this.$refs.dynamicWatermark) {
          if (Object.keys(options).length > 0) {
            this.$refs.dynamicWatermark.updateConfig(options)
          }
          this.$refs.dynamicWatermark.show()
        }
      }

      this.$root.$hideWatermark = () => {
        if (this.$refs.dynamicWatermark) {
          this.$refs.dynamicWatermark.hide()
        }
      }

      this.$root.$refreshWatermark = () => {
        if (this.$refs.dynamicWatermark) {
          this.$refs.dynamicWatermark.refresh()
        }
      }
    },

    initCallApp() {
      const baseDownloadUrl = 'https://bingobook.cn/download'

      this.callAppInstance = new CallApp({
        scheme: {
          protocol: 'bingobook',
          host: 'app'
        },
        intent: {
          package: 'cn.bingobook.app',
          scheme: 'bingobook'
        },
        appstore: 'https://apps.apple.com/cn/app/%E7%BC%A4%E6%9E%9C%E6%95%B0%E5%AD%97%E6%95%99%E6%9D%90-bingobook/id6737821341',
        yingyongbao: 'https://sj.qq.com/appdetail/cn.bingobook.app',
        timeout: 3000,
        fallback: baseDownloadUrl
      })
    },
    toApp() {
      const baseDownloadUrl = 'https://bingobook.cn/download'

      const hash = window.location.hash
      const queryString = hash.split('?')[1]

      const downloadUrlWithParams = queryString ? `${baseDownloadUrl}?${queryString}` : baseDownloadUrl
      window.open(downloadUrlWithParams)
    },
    toAppForBingoBook() {
      const originDownloadUrl = 'https://bingobook.cn/#/download'
      const bingoBookDeeplink = 'https://bingobook.cn/app/book/'

      const hash = window.location.hash
      const queryString = hash.split('?')[1]
      const urlParams = new URLSearchParams(queryString)
      const bookId = urlParams.get('id')

      if (!bookId) {
        window.open(originDownloadUrl)
        return
      }

      const deepLinkFullUrl = `${bingoBookDeeplink}${bookId}`
      // console.log('deepLink Log:网页尝试打开Deep Link', deepLinkFullUrl)

      let appOpened = false
      let timeoutId;

      const cleanup = () => {
        document.removeEventListener('visibilitychange', handleVisibilityChange)
        window.removeEventListener('blur', handleBlur)
        window.removeEventListener('pagehide', handlePause)
      }

      const detectAppOpened = () => {
        if (appOpened) return

        appOpened = true
        if (timeoutId) {
          clearTimeout(timeoutId)
        }
        // console.log('deepLink Log:检测到APP已经打开')
        cleanup()
      }

      const handleVisibilityChange = () => {
        if (document.hidden) {
          detectAppOpened()
        }
      }
      const handleBlur = () => {
        detectAppOpened()
      }
      const handlePause = () => {
        detectAppOpened()
      }

      const addEventListener = () => {
        document.addEventListener('visibilitychange', handleVisibilityChange)
        window.addEventListener('blur', handleBlur)
        window.addEventListener('pagehide', handlePause)
      }

      addEventListener()
      setTimeout(() => {
        window.location.href = deepLinkFullUrl
      }, 100)

      timeoutId = setTimeout(() => {
        cleanup()
        if (!appOpened) {
          // console.log('deepLink Log:APP未安装，跳转至下载页面')
          window.open(originDownloadUrl)
        }
      }, 3000)
    },
    toAppBingoMate() {
      window.open('https://bingomate.com/download')
    },
    openInApp() {
      const bookId = this.getCurrentBookId()
      const scheme = bookId ? `bingobook://app/book/${bookId}` : 'bingobook://app'

      const userAgent = navigator.userAgent.toLowerCase()
      const isIOS = /iphone|ipad|ipod/.test(userAgent)
      const isAndroid = /android/.test(userAgent)

      if (!isIOS && !isAndroid) {
        return
      }

      this.tryOpenAppWithDetection(scheme)
    },
    openBingoBookInApp() {
      const bookId = this.getCurrentBookId()
      const scheme = bookId ? `bingobook://app/book/${bookId}` : 'bingobook://app'

      const userAgent = navigator.userAgent.toLowerCase()
      const isIOS = /iphone|ipad|ipod/.test(userAgent)
      const isAndroid = /android/.test(userAgent)
      const isWechat = isWeChatBrowser()
      const isMobile = isIOS || isAndroid

      // console.log('🚀 唤起APP - 环境检测:', {
      //   isIOS,
      //   isAndroid,
      //   isWechat,
      //   isMobile,
      //   bookId,
      //   scheme
      // })

      if (!isMobile) {
        // console.log('⚠️ 非移动端环境，跳过APP唤起')
        return
      }

      if (isWechat) {
        this.handleWechatAppLaunch(scheme, bookId)
        return
      }

      this.handleBrowserAppLaunch(scheme)
    },

    handleWechatAppLaunch(scheme, bookId) {
      // console.log('📱 微信环境 - 尝试唤起APP', { scheme, bookId })
      if (this.isWxOpenTagSupported()) {
        // console.log('🎯 使用微信开放标签唤起APP')
        this.useWxOpenTag(scheme, bookId)
      } else {
        // console.log('📱 降级到 CallApp 库方案')
        const fallbackUrl = this.getDownloadUrlWithParams()

        if (this.callAppInstance) {
          const originalTimeout = this.callAppInstance.timeout
          this.callAppInstance.timeout = 2000

          const bookId = this.getCurrentBookId()

          const path = bookId ? `book/${bookId}` : ''

          this.callAppInstance.open({
            path: path
          })
          this.callAppInstance.timeout = originalTimeout
        } else {
          this.trySchemeWithFallback(scheme, fallbackUrl, 2000)
        }
      }
    },
    isWxOpenTagSupported() {
      return false
    },
    useWxOpenTag(scheme, bookId) {
      // TODO: 实现微信开放标签逻辑
      // 示例代码结构：
      /*
      const wx = require('weixin-js-sdk')

      // 配置微信 JSSDK
      wx.config({
        // ... 配置参数
        openTagList: ['wx-open-launch-app']
      })

      // 动态创建开放标签
      const openTag = document.createElement('wx-open-launch-app')
      openTag.setAttribute('appid', 'your-app-id')
      openTag.setAttribute('extinfo', scheme)
      // ... 其他配置
      */

      // console.log('🚧 微信开放标签功能待实现', { scheme, bookId })
    },

    handleBrowserAppLaunch(scheme) {
      // console.log('🌐 浏览器环境 - 使用 CallApp 库唤起APP')

      if (!this.callAppInstance) {
        console.warn('⚠️ CallApp 实例未初始化，降级到传统方案')
        this.tryOpenAppWithDetection(scheme)
        return
      }

      const bookId = this.getCurrentBookId()
      const path = bookId ? `book/${bookId}` : ''

      this.callAppInstance.open({
        path: path
      })

      // console.log('📱 CallApp 唤起完成', { path, bookId })
    },

    /**
     * 尝试scheme跳转并提供降级方案
     */
    trySchemeWithFallback(scheme, fallbackUrl, timeout = 3000) {
      let appOpened = false
      let timeoutId

      const cleanup = () => {
        document.removeEventListener('visibilitychange', handleVisibilityChange)
        window.removeEventListener('blur', handleBlur)
        window.removeEventListener('pagehide', handlePause)
      }

      const detectAppOpened = () => {
        if (appOpened) return
        appOpened = true
        if (timeoutId) {
          clearTimeout(timeoutId)
        }
        // console.log('✅ 检测到APP已打开')
        cleanup()
      }

      const handleVisibilityChange = () => {
        if (document.hidden) {
          detectAppOpened()
        }
      }

      const handleBlur = () => {
        detectAppOpened()
      }

      const handlePause = () => {
        detectAppOpened()
      }

      // 添加事件监听
      document.addEventListener('visibilitychange', handleVisibilityChange)
      window.addEventListener('blur', handleBlur)
      window.addEventListener('pagehide', handlePause)

      // 尝试打开APP
      setTimeout(() => {
        window.location.href = scheme
      }, 100)

      // 超时处理
      timeoutId = setTimeout(() => {
        cleanup()
        if (!appOpened) {
          // console.log('⏰ APP唤起超时，跳转下载页面')
          window.open(fallbackUrl)
        }
      }, timeout)
    },

    /**
     * 获取带参数的下载链接
     */
    getDownloadUrlWithParams() {
      const baseDownloadUrl = 'https://bingobook.cn/download'
      const hash = window.location.hash
      const queryString = hash.split('?')[1]
      return queryString ? `${baseDownloadUrl}?${queryString}` : baseDownloadUrl
    },

    getCurrentBookId() {
      const hash = window.location.hash
      const queryString = hash.split('?')[1]
      if (queryString) {
        const urlParams = new URLSearchParams(queryString)
        const bookId = urlParams.get('id')
        return bookId
      }
      return null
    },
    tryOpenAppWithDetection(scheme) {
      let appOpened = false
      let timeoutId

      const cleanup = () => {
        document.removeEventListener('visibilitychange', handleVisibilityChange)
        window.removeEventListener('blur', handleBlur)
        window.removeEventListener('pagehide', handlePause)
      }

      const detectAppOpened = () => {
        if (appOpened) return
        appOpened = true
        if (timeoutId) {
          clearTimeout(timeoutId)
        }
        // console.log('Deep Link: 检测到 APP 已打开')
        cleanup()
      }

      const handleVisibilityChange = () => {
        if (document.hidden) {
          detectAppOpened()
        }
      }

      const handleBlur = () => {
        detectAppOpened()
      }

      const handlePause = () => {
        detectAppOpened()
      }

      document.addEventListener('visibilitychange', handleVisibilityChange)
      window.addEventListener('blur', handleBlur)
      window.addEventListener('pagehide', handlePause)

      setTimeout(() => {
        window.location.href = scheme
      }, 100)

      timeoutId = setTimeout(() => {
        cleanup()
        if (!appOpened) {
          this.toApp()
        }
      }, 3000)
    }
  }
}
</script>
<style>
.mceNonEditable{
  text-indent: 0;
  /** 确保富文本编辑器插入的不可被编辑元素，不受缩进影响 */
}
</style>
<style lang="scss">
:root {
  /** 定义公共变量 */
  --primary-color: #007bff;
  --font-size-XXL: 16px;
  --font-size-XL: 14px;
  --font-size-L: 12px;
  --font-size-M: 10px;
  --font-size-S: 8px;
}
</style>
<style lang="scss" scoped>
.moblie_tips{
  width: 100%;
  height: 120px;
  background: #fff;
  border-bottom: 1px solid #e4e1e1;
  padding: 10px;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 999;
  .logo{
    width: 100px;
    margin-left: 20px;
  }
  p{
    font-size: 40px;
    line-height: 100%;
    margin-left: 20px;
  }
  .open{
    width: 200px;
    height: 80px;
    border-radius: 40px;
    font-size: 29px;
    line-height: 80px;
    text-align: center;
    color: #fff;
    background: linear-gradient(301.29deg, #16D9E3 -50.65%, #30C7EC 8.65%, #46AEF7 75.53%);
    margin-left: 400px;
  }
  .close{
    width: 60px;
    margin-left: 20px;
  }
}

.bottom-app-banner {
  position: fixed !important;
  bottom: 180px;
  left: 0;
  right: 0;
  background: transparent;
  border: none;
  padding: 0 48px;
  z-index: 9999;
}

.bottom-banner-button {
  width: 100%;
  max-width: none;
  margin: 0;
  background: linear-gradient(135deg, #ffb366 0%, #ff9a56 100%);
  border: none;
  border-radius: 50px;
  padding: 20px 20px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 2px 12px rgba(255, 179, 102, 0.3);
}

.bottom-banner-button:hover {
  background: linear-gradient(135deg, #ff9a56 0%, #ff8a42 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(255, 179, 102, 0.4);
}

.bottom-banner-button:active {
  transform: translateY(0);
}

.bottom-app-icon {
  width: 100px;
  height: 100px;
  border-radius: 16px;
  background: white;
  margin-right: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  color: #ff8a42;

  img {
    width: 100%;
    height: 100%;
    border-radius: 16px;
    object-fit: cover;
  }
}

.bottom-app-info {
  flex: 1;
  color: white;
}

.bottom-app-info h4 {
  font-size: 22px;
  margin-bottom: 0;
  font-weight: 700;
  color: white;
  text-align: center;
}

@media (max-width: 480px) {
  .bottom-banner-button {
    width: 100%;
    max-width: none;
    padding: 20px 20px;
  }

  .bottom-app-icon {
    width: 100px;
    height: 100px;
    font-size: 40px;
    border-radius: 16px;
    margin-right: 20px;

    img {
      border-radius: 16px;
    }
  }

  .bottom-app-info h4 {
    font-size: 22px;
  }
}
</style>
